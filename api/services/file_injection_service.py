"""
File Injection Service

This module provides comprehensive file injection capabilities for the TurdParty malware
analysis platform. It handles the complete lifecycle of file injection operations from
upload through processing to cleanup.

Key Features:
    - File upload and storage management
    - Injection status tracking and monitoring
    - Asynchronous processing with progress updates
    - File validation and security checks
    - Target path and permission management
    - Comprehensive error handling and recovery

File Injection Workflow:
    1. File upload and validation
    2. Storage in secure upload directory
    3. Injection record creation with metadata
    4. Asynchronous processing with status updates
    5. File copying to target location
    6. Permission setting and validation
    7. Completion notification and cleanup

Security Features:
    - File hash calculation for integrity verification
    - Secure file storage with unique identifiers
    - Permission validation and enforcement
    - Comprehensive audit logging
    - Error handling with detailed reporting

Storage Management:
    - Configurable upload directory
    - Unique file naming to prevent conflicts
    - Automatic cleanup on deletion
    - File size and type validation

Status Tracking:
    - PENDING: Initial upload completed
    - IN_PROGRESS: Processing active
    - COMPLETED: Successfully injected
    - FAILED: Processing failed with error details

Integration:
    - Database persistence for production use
    - ELK stack logging for monitoring
    - Background task processing via Celery
    - VM integration for target environment access

Mock Implementation:
    This service includes in-memory storage for development and testing.
    Production deployment uses PostgreSQL for persistence and MinIO for file storage.
"""
import os
import uuid
import hashlib
import logging
import asyncio
import re
import html
from datetime import datetime, timezone
from typing import List, Optional, Dict, Any
from pathlib import Path

from api.models.file_injection import (
    FileInjectionCreate,
    FileInjectionResponse,
    FileInjectionStatus,
    InjectionStatus
)

logger = logging.getLogger(__name__)

class FileInjectionService:
    """
    Service for managing file injection operations.

    Provides comprehensive file injection capabilities including upload,
    processing, status tracking, and cleanup. Handles the complete
    lifecycle of file injection operations in the TurdParty platform.
    """
    
    def __init__(self):
        """
        Initialize the file injection service.

        Sets up upload directory and initializes in-memory storage
        for development and testing purposes.
        """
        self.upload_dir = Path(os.getenv("FILE_UPLOAD_DIR", "/app/uploads"))
        self.upload_dir.mkdir(parents=True, exist_ok=True)

        # In-memory storage for demo purposes
        # In production, this would be a database
        self._injections: Dict[str, Dict[str, Any]] = {}

        # Security configuration
        self.max_file_size = 50 * 1024 * 1024  # 50MB limit
        self.max_description_length = 500

    def _sanitize_input(self, text: Optional[str]) -> Optional[str]:
        """Sanitize user input to prevent injection attacks"""
        if not text:
            return text

        # Remove dangerous characters and patterns
        dangerous_patterns = [
            r'[;&|`$(){}[\]<>]',  # Shell metacharacters
            r'[\r\n]',  # Actual newline characters
            r'\\[rn]',  # Newline escapes
            r'<script[^>]*>.*?</script>',  # Script tags
            r'javascript:',  # JavaScript URLs
            r'on\w+\s*=',  # Event handlers
        ]

        sanitized = text
        for pattern in dangerous_patterns:
            sanitized = re.sub(pattern, '', sanitized, flags=re.IGNORECASE | re.DOTALL)

        # HTML escape remaining content
        sanitized = html.escape(sanitized)

        # Truncate to max length
        if len(sanitized) > self.max_description_length:
            sanitized = sanitized[:self.max_description_length]

        return sanitized

    def _validate_file_content(self, content: bytes, filename: str) -> Dict[str, Any]:
        """Validate file content for security issues"""
        validation_result = {
            "valid": True,
            "warnings": [],
            "errors": []
        }

        # Check file size
        if len(content) > self.max_file_size:
            validation_result["valid"] = False
            validation_result["errors"].append(f"File size {len(content)} exceeds maximum allowed size {self.max_file_size}")

        # Check for null bytes
        if b'\x00' in content:
            validation_result["warnings"].append("File contains null bytes")

        # Check for potentially dangerous content patterns
        dangerous_patterns = [
            (b'rm -rf', "Destructive file operations detected"),
            (b'curl.*|.*bash', "Remote code execution pattern detected"),
            (b'chmod 777', "Dangerous permission changes detected"),
            (b'/etc/passwd', "System file access detected"),
            (b'/etc/shadow', "Sensitive file access detected"),
            (b'while true.*fork', "Fork bomb pattern detected"),
            (b'dd if=/dev/zero', "Disk filling operation detected"),
        ]

        content_lower = content.lower()
        for pattern, message in dangerous_patterns:
            if re.search(pattern, content_lower):
                validation_result["warnings"].append(message)

        return validation_result

    async def get_all(
        self,
        skip: int = 0,
        limit: int = 100,
        status_filter: Optional[str] = None,
        authenticated: bool = False
    ) -> List[FileInjectionResponse]:
        """Get all file injections with optional filtering and security masking"""
        injections = list(self._injections.values())

        # Apply status filter if provided
        if status_filter:
            injections = [
                inj for inj in injections
                if inj.get("status") == status_filter
            ]

        # Apply pagination
        injections = injections[skip:skip + limit]

        # Mask sensitive data if not authenticated
        if not authenticated:
            for injection in injections:
                if "file_hash" in injection:
                    injection["file_hash"] = "***"

        return [
            FileInjectionResponse(**injection)
            for injection in injections
        ]
    
    async def get_by_id(self, injection_id: str) -> Optional[FileInjectionResponse]:
        """Get a file injection by ID"""
        injection_data = self._injections.get(injection_id)
        if not injection_data:
            return None
        
        return FileInjectionResponse(**injection_data)
    
    async def create_injection(
        self,
        injection_data: FileInjectionCreate,
        file_content: bytes
    ) -> FileInjectionResponse:
        """Create a new file injection with security validation"""

        # Validate file content for security issues
        validation_result = self._validate_file_content(file_content, injection_data.filename)
        if not validation_result["valid"]:
            error_msg = "; ".join(validation_result["errors"])
            raise ValueError(f"File validation failed: {error_msg}")

        # Sanitize description input
        sanitized_description = self._sanitize_input(injection_data.description)

        # Generate unique ID
        injection_id = str(uuid.uuid4())

        # Calculate file hash
        file_hash = hashlib.sha256(file_content).hexdigest()

        # Save file to upload directory
        file_path = self.upload_dir / f"{injection_id}_{injection_data.filename}"
        with open(file_path, "wb") as f:
            f.write(file_content)

        # Create injection record
        now = datetime.now(timezone.utc)
        injection_record = {
            "id": injection_id,
            "filename": injection_data.filename,
            "target_path": injection_data.target_path,
            "permissions": injection_data.permissions,
            "status": InjectionStatus.PENDING,
            "description": sanitized_description,  # Use sanitized description
            "created_at": now,
            "updated_at": now,
            "file_size": len(file_content),
            "file_hash": file_hash,
            "file_path": str(file_path),
            "security_warnings": validation_result.get("warnings", [])  # Include security warnings
        }

        self._injections[injection_id] = injection_record

        # Log security warnings if any
        if validation_result.get("warnings"):
            logger.warning(f"Security warnings for injection {injection_id}: {validation_result['warnings']}")

        logger.info(f"Created file injection {injection_id} for file {injection_data.filename}")

        return FileInjectionResponse(**injection_record)
    
    async def get_status(self, injection_id: str) -> Optional[FileInjectionStatus]:
        """Get the status of a file injection"""
        injection_data = self._injections.get(injection_id)
        if not injection_data:
            return None
        
        # Calculate progress based on status
        progress_map = {
            InjectionStatus.PENDING: 0,
            InjectionStatus.IN_PROGRESS: 50,
            InjectionStatus.COMPLETED: 100,
            InjectionStatus.FAILED: 0
        }
        
        return FileInjectionStatus(
            id=injection_id,
            status=injection_data["status"],
            progress=progress_map.get(injection_data["status"], 0),
            message=injection_data.get("message", ""),
            details=injection_data.get("details", {}),
            updated_at=injection_data["updated_at"]
        )
    
    async def process_injection(self, injection_id: str) -> FileInjectionStatus:
        """Process a file injection"""
        injection_data = self._injections.get(injection_id)
        if not injection_data:
            raise ValueError(f"Injection {injection_id} not found")
        
        if injection_data["status"] != InjectionStatus.PENDING:
            raise ValueError(f"Injection {injection_id} is not in pending status")
        
        try:
            # Update status to in progress
            injection_data["status"] = InjectionStatus.IN_PROGRESS
            injection_data["updated_at"] = datetime.now(timezone.utc)
            injection_data["message"] = "Processing file injection"
            
            logger.info(f"Processing injection {injection_id}")
            
            # Process file injection steps
            await self._process_injection_steps(injection_id)

            # Copy file to target location
            target_path = Path(injection_data["target_path"])
            source_path = Path(injection_data["file_path"])

            # Create target directory if it doesn't exist
            target_path.parent.mkdir(parents=True, exist_ok=True)

            # Copy the file
            import shutil
            shutil.copy2(source_path, target_path)
            logger.info(f"Copied {source_path} to {target_path}")

            # Set permissions
            permissions = injection_data["permissions"]
            if permissions:
                # Convert string permissions to octal
                if isinstance(permissions, str):
                    perm_octal = int(permissions, 8)
                else:
                    perm_octal = permissions
                target_path.chmod(perm_octal)
                logger.info(f"Set permissions {permissions} on {target_path}")
            
            # Update status to completed
            injection_data["status"] = InjectionStatus.COMPLETED
            injection_data["updated_at"] = datetime.now(timezone.utc)
            injection_data["message"] = "File injection completed successfully"
            injection_data["details"] = {
                "file_path": str(source_path),
                "target_path": str(target_path),
                "permissions": permissions,
                "processed_at": datetime.now(timezone.utc).isoformat()
            }
            
            logger.info(f"Completed injection {injection_id}")
            
        except Exception as e:
            # Update status to failed
            injection_data["status"] = InjectionStatus.FAILED
            injection_data["updated_at"] = datetime.now(timezone.utc)
            injection_data["message"] = f"Processing failed: {str(e)}"
            injection_data["details"] = {"error": str(e)}
            
            logger.error(f"Failed to process injection {injection_id}: {str(e)}")
            raise
        
        return await self.get_status(injection_id)
    
    async def _process_injection(self, injection_id: str):
        """Process file injection with production implementation"""
        from services.workers.tasks.vm_management import inject_file_via_vagrant, inject_file_via_ssh
        from utils.service_urls import ServiceURLManager

        injection_data = self._injections[injection_id]

        steps = [
            "Validating file and VM",
            "Establishing VM connection",
            "Transferring file to VM",
            "Verifying file transfer",
            "Setting file permissions"
        ]

        try:
            for i, step in enumerate(steps):
                injection_data["message"] = step
                injection_data["details"] = {
                    "current_step": step,
                    "step_number": i + 1,
                    "total_steps": len(steps)
                }
                injection_data["updated_at"] = datetime.now(timezone.utc)

                if step == "Validating file and VM":
                    # Validate source file exists
                    file_path = injection_data["file_path"]
                    if not os.path.exists(file_path):
                        raise FileNotFoundError(f"Source file not found: {file_path}")

                    # Validate VM information
                    vm_id = injection_data["vm_id"]
                    if not vm_id:
                        raise ValueError("VM ID is required for file injection")

                    await asyncio.sleep(0.5)

                elif step == "Establishing VM connection":
                    # Get VM details (simplified for now)
                    vm_dir = f"/tmp/turdparty_vms/turdparty-vm-{vm_id[:8]}"
                    injection_data["vm_dir"] = vm_dir

                    await asyncio.sleep(1.0)

                elif step == "Transferring file to VM":
                    # Perform file transfer
                    file_path = injection_data["file_path"]
                    target_path = injection_data["target_path"]
                    vm_dir = injection_data.get("vm_dir")

                    if vm_dir:
                        # Use Vagrant for file transfer
                        result = inject_file_via_vagrant(
                            f"turdparty-vm-{vm_id[:8]}",
                            file_path,
                            target_path,
                            vm_dir
                        )
                    else:
                        # Use SSH (future implementation)
                        result = {"success": False, "error": "SSH injection not implemented"}

                    if not result.get("success"):
                        raise Exception(f"File transfer failed: {result.get('error')}")

                    injection_data["transfer_result"] = result
                    await asyncio.sleep(2.0)

                elif step == "Verifying file transfer":
                    # Verify file was transferred successfully
                    transfer_result = injection_data.get("transfer_result", {})
                    if not transfer_result.get("verified", False):
                        logger.warning(f"File transfer verification failed for injection {injection_id}")

                    await asyncio.sleep(0.5)

                elif step == "Setting file permissions":
                    # Set appropriate file permissions (Windows specific)
                    await asyncio.sleep(0.5)

        except Exception as e:
            logger.error(f"File injection processing failed: {e}")
            injection_data["status"] = "failed"
            injection_data["message"] = f"Processing failed: {str(e)}"
            injection_data["details"] = {"error": str(e)}
            raise
    
    async def delete(self, injection_id: str):
        """Delete a file injection"""
        injection_data = self._injections.get(injection_id)
        if not injection_data:
            raise ValueError(f"Injection {injection_id} not found")
        
        # Remove uploaded file
        file_path = Path(injection_data["file_path"])
        if file_path.exists():
            file_path.unlink()
        
        # Remove from storage
        del self._injections[injection_id]
        
        logger.info(f"Deleted injection {injection_id}")
