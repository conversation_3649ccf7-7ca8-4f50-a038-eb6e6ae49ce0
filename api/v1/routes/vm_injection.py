"""
VM Injection API Endpoints

This module provides REST API endpoints for managing file injection into virtual machines.
It handles the creation, monitoring, and management of VM injection operations as part
of the TurdParty malware analysis workflow.

Key Features:
    - Create VM injection tasks for file analysis
    - Monitor injection status and progress
    - List and filter injection operations
    - Process pending injections
    - Clean up completed injection records

The VM injection process involves:
    1. File upload and validation
    2. VM template selection and configuration
    3. File injection into the VM environment
    4. Monitoring and status tracking
    5. Result collection and cleanup

API Endpoints:
    - GET /virtual-machines/injections/ - List all injections
    - POST /virtual-machines/injections/ - Create new injection
    - GET /virtual-machines/injections/{id} - Get injection details
    - POST /virtual-machines/injections/{id}/process - Process injection
    - DELETE /virtual-machines/injections/{id} - Delete injection record
"""
import os
import uuid
import logging
from datetime import datetime
from typing import Optional, List
from fastapi import APIRouter, HTTPException, status
from pydantic import BaseModel

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/virtual-machines/injections",
    tags=["vm_injection"],
    responses={404: {"description": "VM injection not found"}},
)

class VMInjectionRequest(BaseModel):
    """
    Request model for creating a new VM injection.

    Attributes:
        file_upload_id: UUID of the uploaded file to inject
        template_id: UUID of the VM template to use for injection
        target_path: Target path in the VM where the file will be placed
        permissions: File permissions in octal format (default: 0755)
    """
    file_upload_id: str
    template_id: str
    target_path: str
    permissions: str = "0755"

class VMInjectionResponse(BaseModel):
    """
    Response model for VM injection operations.

    Attributes:
        injection_id: Unique identifier for the injection operation
        file_upload_id: UUID of the uploaded file being injected
        template_id: UUID of the VM template used for injection
        target_path: Target path in the VM where the file is placed
        permissions: File permissions in octal format
        status: Current status (pending, processing, completed, failed)
        created_at: ISO timestamp of injection creation
        message: Human-readable status message
    """
    injection_id: str
    file_upload_id: str
    template_id: str
    target_path: str
    permissions: str
    status: str
    created_at: str
    message: str

class VMInjectionListResponse(BaseModel):
    """
    Response model for paginated VM injection listings.

    Attributes:
        injections: List of VM injection objects
        total: Total number of injections matching the query
        skip: Number of records skipped for pagination
        limit: Maximum number of records returned
    """
    injections: List[VMInjectionResponse]
    total: int
    skip: int
    limit: int

# Mock injection storage
mock_injections = []

@router.get("/", response_model=VMInjectionListResponse)
async def list_vm_injections(
    skip: int = 0,
    limit: int = 100,
    status_filter: Optional[str] = None
):
    """
    List all VM injections with optional filtering.
    
    Parameters:
    - **skip**: Number of records to skip (for pagination)
    - **limit**: Maximum number of records to return
    - **status_filter**: Optional status to filter by
    
    Returns:
    - Paginated list of VM injections
    """
    try:
        filtered_injections = mock_injections
        if status_filter:
            filtered_injections = [inj for inj in mock_injections if inj["status"] == status_filter]
        
        total = len(filtered_injections)
        injections = filtered_injections[skip:skip + limit]
        
        return VMInjectionListResponse(
            injections=injections,
            total=total,
            skip=skip,
            limit=limit
        )
    except Exception as e:
        logger.error(f"Error listing VM injections: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list VM injections"
        )

@router.get("/{injection_id}", response_model=VMInjectionResponse)
async def get_vm_injection(injection_id: str):
    """
    Get a specific VM injection by ID.
    
    Parameters:
    - **injection_id**: UUID of the VM injection to retrieve
    
    Returns:
    - VM injection object with all details
    """
    try:
        injection = next((inj for inj in mock_injections if inj["injection_id"] == injection_id), None)
        if not injection:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"VM injection with id {injection_id} not found"
            )
        return injection
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving VM injection {injection_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve VM injection"
        )

@router.post("/", response_model=VMInjectionResponse, status_code=status.HTTP_201_CREATED)
async def create_vm_injection(request: VMInjectionRequest):
    """
    Create a new VM injection.
    
    This endpoint creates a VM injection task that will inject a file
    into a VM template and process it.
    
    Parameters:
    - **file_upload_id**: ID of the uploaded file to inject
    - **template_id**: ID of the VM template to use
    - **target_path**: Target path in the VM for the file
    - **permissions**: File permissions (default: 0755)
    
    Returns:
    - Created VM injection object
    """
    try:
        # Generate unique injection ID
        injection_id = str(uuid.uuid4())
        
        # Create injection record
        injection = {
            "injection_id": injection_id,
            "file_upload_id": request.file_upload_id,
            "template_id": request.template_id,
            "target_path": request.target_path,
            "permissions": request.permissions,
            "status": "pending",
            "created_at": datetime.utcnow().isoformat(),
            "message": "VM injection created successfully"
        }
        
        # Add to mock storage
        mock_injections.append(injection)
        
        logger.info(f"VM injection created successfully: {injection_id}")
        return injection
        
    except Exception as e:
        logger.error(f"Error creating VM injection: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create VM injection: {str(e)}"
        )

@router.post("/{injection_id}/process")
async def process_vm_injection(injection_id: str):
    """
    Process a pending VM injection.
    
    This endpoint triggers the actual VM injection process.
    
    Parameters:
    - **injection_id**: UUID of the VM injection to process
    
    Returns:
    - Updated injection status
    """
    try:
        injection = next((inj for inj in mock_injections if inj["injection_id"] == injection_id), None)
        if not injection:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"VM injection with id {injection_id} not found"
            )
        
        # Update status to processing
        injection["status"] = "processing"
        injection["message"] = "VM injection is being processed"
        
        logger.info(f"VM injection processing started: {injection_id}")
        return injection
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing VM injection {injection_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to process VM injection: {str(e)}"
        )

@router.delete("/{injection_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_vm_injection(injection_id: str):
    """
    Delete a VM injection record.
    
    Parameters:
    - **injection_id**: UUID of the VM injection to delete
    
    Returns:
    - No content (204)
    """
    try:
        global mock_injections
        original_count = len(mock_injections)
        mock_injections = [inj for inj in mock_injections if inj["injection_id"] != injection_id]
        
        if len(mock_injections) == original_count:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"VM injection with id {injection_id} not found"
            )
        
        logger.info(f"VM injection deleted successfully: {injection_id}")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting VM injection {injection_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete VM injection"
        )
