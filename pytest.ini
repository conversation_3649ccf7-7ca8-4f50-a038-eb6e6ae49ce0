[tool:pytest]
# Pytest configuration for TurdParty integration and Fibratus testing

# Test discovery
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Minimum version
minversion = 6.0

# Add options
addopts =
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --durations=10
    --color=yes

# Markers for test categorization
markers =
    unit: Unit tests (fast, no external dependencies)
    integration: Integration tests (slower, requires infrastructure)
    fibratus: Fibratus-specific tests (requires VM and monitoring)
    slow: Slow tests (may take several minutes)
    api: API endpoint tests
    database: Database integration tests
    elasticsearch: Elasticsearch integration tests
    vm: Virtual machine tests
    real: Tests using real binaries and data (no mocks)
    websocket: WebSocket functionality tests
    performance: Performance and benchmark tests
    benchmark: Benchmark tests (requires pytest-benchmark)
    scalability: Scalability tests
    resource: Resource utilization tests
    docker: Tests requiring Docker
    vagrant: Tests requiring Vagrant
    real_vm: Tests using real VMs (not mocked)
    parallel: Tests safe for parallel execution
    serial: Tests that must run serially
    concurrent: Tests that test concurrent behavior
    stress: Stress tests with high resource usage
    mock: Tests using mocked components
    security: Security tests
    file_injection: File injection workflow tests
    property: Property-based tests
    e2e: End-to-end tests
    load: Load tests
    fibratus: Fibratus integration tests
    windows: Windows-specific tests

# Test timeout (30 minutes for full integration tests)
timeout = 1800

# Asyncio configuration
asyncio_mode = auto

# Logging configuration
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Warnings
filterwarnings =
    ignore::DeprecationWarning:logstash.*
    ignore::DeprecationWarning:python-logstash.*
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:websockets.*
    ignore::UserWarning:httpx.*
    ignore::DeprecationWarning:httpx.*
    ignore::DeprecationWarning:starlette.*
    ignore::DeprecationWarning:fastapi.*
    ignore::UserWarning:pydantic.*
    ignore:coroutine.*was never awaited:RuntimeWarning
    ignore::pytest.PytestUnraisableExceptionWarning
    ignore::ResourceWarning

# Coverage configuration
[coverage:run]
source = api
omit = 
    */tests/*
    */venv/*
    */env/*
    */__pycache__/*
    */migrations/*
    */settings/*
    */manage.py
    */wsgi.py
    */asgi.py

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod

[coverage:html]
directory = htmlcov
