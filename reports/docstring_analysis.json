{"summary": {"total_files_analyzed": 124, "total_elements": 1208, "total_documented_elements": 1116, "overall_coverage_percentage": 92.38, "files_with_critical_issues": 0, "files_needing_improvement": 3, "files_with_good_coverage": 121}, "issue_counts": {"critical": 0, "high": 90, "medium": 2, "low": 0, "total": 92}, "files_by_category": {"critical_coverage": [], "needs_improvement": [{"file_path": "api/middleware/ecs_logging.py", "total_elements": 14, "documented_elements": 10, "coverage_percentage": 71.42857142857143, "issues": [{"file_path": "api/middleware/ecs_logging.py", "line_number": 233, "element_type": "class", "element_name": "ECSFormatter", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "api/middleware/ecs_logging.py", "line_number": 234, "element_type": "method", "element_name": "format", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "api/middleware/ecs_logging.py", "line_number": 438, "element_type": "class", "element_name": "ECSFormatter", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "api/middleware/ecs_logging.py", "line_number": 439, "element_type": "method", "element_name": "format", "issue_description": "Missing docstring", "severity": "high"}], "has_module_docstring": true}, {"file_path": "services/api/src/utils/logging.py", "total_elements": 21, "documented_elements": 15, "coverage_percentage": 71.42857142857143, "issues": [{"file_path": "services/api/src/utils/logging.py", "line_number": 1, "element_type": "module", "element_name": "<module>", "issue_description": "Contains placeholder text: 'pass'", "severity": "high"}, {"file_path": "services/api/src/utils/logging.py", "line_number": 173, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "services/api/src/utils/logging.py", "line_number": 209, "element_type": "function", "element_name": "send_wrapper", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "services/api/src/utils/logging.py", "line_number": 272, "element_type": "function", "element_name": "decorator", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "services/api/src/utils/logging.py", "line_number": 274, "element_type": "function", "element_name": "async_wrapper", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "services/api/src/utils/logging.py", "line_number": 296, "element_type": "function", "element_name": "sync_wrapper", "issue_description": "Missing docstring", "severity": "high"}], "has_module_docstring": false}, {"file_path": "scripts/test-service-urls.py", "total_elements": 4, "documented_elements": 3, "coverage_percentage": 75.0, "issues": [{"file_path": "scripts/test-service-urls.py", "line_number": 67, "element_type": "function", "element_name": "test_service_url_manager", "issue_description": "Contains placeholder text: 'pass'", "severity": "high"}], "has_module_docstring": true}], "good_coverage": [{"file_path": "docker/playwright/health_server.py", "total_elements": 10, "documented_elements": 8, "coverage_percentage": 80.0, "issues": [{"file_path": "docker/playwright/health_server.py", "line_number": 143, "element_type": "method", "element_name": "serve_report_file", "issue_description": "Single-line docstring too brief", "severity": "high"}, {"file_path": "docker/playwright/health_server.py", "line_number": 165, "element_type": "method", "element_name": "send_404", "issue_description": "Single-line docstring too brief", "severity": "high"}], "has_module_docstring": true}, {"file_path": "scripts/run-5-binary-analysis.py", "total_elements": 10, "documented_elements": 8, "coverage_percentage": 80.0, "issues": [{"file_path": "scripts/run-5-binary-analysis.py", "line_number": 305, "element_type": "function", "element_name": "main", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "scripts/run-5-binary-analysis.py", "line_number": 20, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}], "has_module_docstring": true}, {"file_path": "scripts/test-windows-dev-binaries.py", "total_elements": 15, "documented_elements": 12, "coverage_percentage": 80.0, "issues": [{"file_path": "scripts/test-windows-dev-binaries.py", "line_number": 71, "element_type": "class", "element_name": "WindowsDevBinaryTester", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "scripts/test-windows-dev-binaries.py", "line_number": 503, "element_type": "function", "element_name": "main", "issue_description": "Single-line docstring too brief", "severity": "high"}, {"file_path": "scripts/test-windows-dev-binaries.py", "line_number": 72, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}], "has_module_docstring": true}, {"file_path": "services/api/src/services/vm_manager.py", "total_elements": 20, "documented_elements": 16, "coverage_percentage": 80.0, "issues": [{"file_path": "services/api/src/services/vm_manager.py", "line_number": 19, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "services/api/src/services/vm_manager.py", "line_number": 178, "element_type": "method", "element_name": "stop_vm", "issue_description": "Single-line docstring too brief", "severity": "high"}, {"file_path": "services/api/src/services/vm_manager.py", "line_number": 348, "element_type": "method", "element_name": "_start_vagrant_vm", "issue_description": "Single-line docstring too brief", "severity": "medium"}, {"file_path": "services/api/src/services/vm_manager.py", "line_number": 359, "element_type": "method", "element_name": "_stop_vagrant_vm", "issue_description": "Single-line docstring too brief", "severity": "medium"}], "has_module_docstring": true}, {"file_path": "api/models/vm_management.py", "total_elements": 26, "documented_elements": 21, "coverage_percentage": 80.76923076923077, "issues": [{"file_path": "api/models/vm_management.py", "line_number": 9, "element_type": "class", "element_name": "VMType", "issue_description": "Single-line docstring too brief", "severity": "high"}, {"file_path": "api/models/vm_management.py", "line_number": 15, "element_type": "class", "element_name": "VMStatus", "issue_description": "Single-line docstring too brief", "severity": "high"}, {"file_path": "api/models/vm_management.py", "line_number": 86, "element_type": "class", "element_name": "VMMetrics", "issue_description": "Single-line docstring too brief", "severity": "high"}, {"file_path": "api/models/vm_management.py", "line_number": 163, "element_type": "class", "element_name": "ProcessInfo", "issue_description": "Single-line docstring too brief", "severity": "high"}, {"file_path": "api/models/vm_management.py", "line_number": 171, "element_type": "class", "element_name": "NetworkStats", "issue_description": "Single-line docstring too brief", "severity": "high"}], "has_module_docstring": true}, {"file_path": "tests/integration/run_integration_tests.py", "total_elements": 11, "documented_elements": 9, "coverage_percentage": 81.81818181818183, "issues": [{"file_path": "tests/integration/run_integration_tests.py", "line_number": 373, "element_type": "function", "element_name": "main", "issue_description": "Single-line docstring too brief", "severity": "high"}, {"file_path": "tests/integration/run_integration_tests.py", "line_number": 34, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}], "has_module_docstring": true}, {"file_path": "api/v1/routes/vm_files.py", "total_elements": 12, "documented_elements": 10, "coverage_percentage": 83.33333333333334, "issues": [{"file_path": "api/v1/routes/vm_files.py", "line_number": 436, "element_type": "function", "element_name": "delete_vm_file", "issue_description": "Single-line docstring too brief", "severity": "high"}, {"file_path": "api/v1/routes/vm_files.py", "line_number": 83, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}], "has_module_docstring": true}, {"file_path": "scripts/generate-coverage-badge.py", "total_elements": 6, "documented_elements": 5, "coverage_percentage": 83.33333333333334, "issues": [{"file_path": "scripts/generate-coverage-badge.py", "line_number": 97, "element_type": "function", "element_name": "main", "issue_description": "Single-line docstring too brief", "severity": "high"}], "has_module_docstring": true}, {"file_path": "scripts/run-notepadpp-celery-workflow.py", "total_elements": 12, "documented_elements": 10, "coverage_percentage": 83.33333333333334, "issues": [{"file_path": "scripts/run-notepadpp-celery-workflow.py", "line_number": 318, "element_type": "function", "element_name": "main", "issue_description": "Single-line docstring too brief", "severity": "high"}, {"file_path": "scripts/run-notepadpp-celery-workflow.py", "line_number": 25, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}], "has_module_docstring": true}, {"file_path": "services/report_generator.py", "total_elements": 19, "documented_elements": 16, "coverage_percentage": 84.21052631578947, "issues": [{"file_path": "services/report_generator.py", "line_number": 387, "element_type": "function", "element_name": "main", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "services/report_generator.py", "line_number": 20, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "services/report_generator.py", "line_number": 381, "element_type": "method", "element_name": "cleanup", "issue_description": "Single-line docstring too brief", "severity": "high"}], "has_module_docstring": true}, {"file_path": "scripts/run-10-binaries-rich-cli.py", "total_elements": 13, "documented_elements": 11, "coverage_percentage": 84.61538461538461, "issues": [{"file_path": "scripts/run-10-binaries-rich-cli.py", "line_number": 32, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "scripts/run-10-binaries-rich-cli.py", "line_number": 155, "element_type": "method", "element_name": "print_header", "issue_description": "Single-line docstring too brief", "severity": "high"}], "has_module_docstring": true}, {"file_path": "scripts/run-notepadpp-workflow.py", "total_elements": 13, "documented_elements": 11, "coverage_percentage": 84.61538461538461, "issues": [{"file_path": "scripts/run-notepadpp-workflow.py", "line_number": 470, "element_type": "function", "element_name": "main", "issue_description": "Single-line docstring too brief", "severity": "high"}, {"file_path": "scripts/run-notepadpp-workflow.py", "line_number": 24, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}], "has_module_docstring": true}, {"file_path": "scripts/test-end-to-end-workflow.py", "total_elements": 13, "documented_elements": 11, "coverage_percentage": 84.61538461538461, "issues": [{"file_path": "scripts/test-end-to-end-workflow.py", "line_number": 447, "element_type": "function", "element_name": "main", "issue_description": "Single-line docstring too brief", "severity": "high"}, {"file_path": "scripts/test-end-to-end-workflow.py", "line_number": 25, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}], "has_module_docstring": true}, {"file_path": "scripts/test-reporting-api.py", "total_elements": 13, "documented_elements": 11, "coverage_percentage": 84.61538461538461, "issues": [{"file_path": "scripts/test-reporting-api.py", "line_number": 342, "element_type": "function", "element_name": "main", "issue_description": "Single-line docstring too brief", "severity": "high"}, {"file_path": "scripts/test-reporting-api.py", "line_number": 23, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}], "has_module_docstring": true}, {"file_path": "services/workers/vm_manager.py", "total_elements": 13, "documented_elements": 11, "coverage_percentage": 84.61538461538461, "issues": [{"file_path": "services/workers/vm_manager.py", "line_number": 19, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "services/workers/vm_manager.py", "line_number": 178, "element_type": "method", "element_name": "stop_vm", "issue_description": "Single-line docstring too brief", "severity": "high"}], "has_module_docstring": true}, {"file_path": "services/monitoring/vm-agent/agent.py", "total_elements": 27, "documented_elements": 23, "coverage_percentage": 85.18518518518519, "issues": [{"file_path": "services/monitoring/vm-agent/agent.py", "line_number": 410, "element_type": "function", "element_name": "main", "issue_description": "Single-line docstring too brief", "severity": "high"}, {"file_path": "services/monitoring/vm-agent/agent.py", "line_number": 199, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "services/monitoring/vm-agent/agent.py", "line_number": 244, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "services/monitoring/vm-agent/agent.py", "line_number": 326, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}], "has_module_docstring": true}, {"file_path": "scripts/download-binaries.py", "total_elements": 7, "documented_elements": 6, "coverage_percentage": 85.71428571428571, "issues": [{"file_path": "scripts/download-binaries.py", "line_number": 18, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}], "has_module_docstring": true}, {"file_path": "scripts/generate-license-docs.py", "total_elements": 14, "documented_elements": 12, "coverage_percentage": 85.71428571428571, "issues": [{"file_path": "scripts/generate-license-docs.py", "line_number": 515, "element_type": "function", "element_name": "main", "issue_description": "Single-line docstring too brief", "severity": "high"}, {"file_path": "scripts/generate-license-docs.py", "line_number": 21, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}], "has_module_docstring": true}, {"file_path": "scripts/mock-replacement-tracker.py", "total_elements": 14, "documented_elements": 12, "coverage_percentage": 85.71428571428571, "issues": [{"file_path": "scripts/mock-replacement-tracker.py", "line_number": 294, "element_type": "function", "element_name": "main", "issue_description": "Single-line docstring too brief", "severity": "high"}, {"file_path": "scripts/mock-replacement-tracker.py", "line_number": 21, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}], "has_module_docstring": true}, {"file_path": "scripts/test-health-checks.py", "total_elements": 14, "documented_elements": 12, "coverage_percentage": 85.71428571428571, "issues": [{"file_path": "scripts/test-health-checks.py", "line_number": 1, "element_type": "module", "element_name": "<module>", "issue_description": "Contains placeholder text: 'pass'", "severity": "high"}, {"file_path": "scripts/test-health-checks.py", "line_number": 127, "element_type": "method", "element_name": "run_all_tests", "issue_description": "Contains placeholder text: 'pass'", "severity": "high"}], "has_module_docstring": false}, {"file_path": "scripts/test-vagrant-grpc-connectivity.py", "total_elements": 7, "documented_elements": 6, "coverage_percentage": 85.71428571428571, "issues": [{"file_path": "scripts/test-vagrant-grpc-connectivity.py", "line_number": 152, "element_type": "function", "element_name": "main", "issue_description": "Single-line docstring too brief", "severity": "high"}], "has_module_docstring": true}, {"file_path": "services/monitoring/fibratus/comprehensive_monitor.py", "total_elements": 7, "documented_elements": 6, "coverage_percentage": 85.71428571428571, "issues": [{"file_path": "services/monitoring/fibratus/comprehensive_monitor.py", "line_number": 18, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}], "has_module_docstring": true}, {"file_path": "services/workers/src/models/vm_instance.py", "total_elements": 7, "documented_elements": 6, "coverage_percentage": 85.71428571428571, "issues": [{"file_path": "services/workers/src/models/vm_instance.py", "line_number": 89, "element_type": "method", "element_name": "__repr__", "issue_description": "Missing docstring", "severity": "high"}], "has_module_docstring": true}, {"file_path": "scripts/inject-files.py", "total_elements": 8, "documented_elements": 7, "coverage_percentage": 87.5, "issues": [{"file_path": "scripts/inject-files.py", "line_number": 19, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}], "has_module_docstring": true}, {"file_path": "services/api/src/routes/v1/admin.py", "total_elements": 8, "documented_elements": 7, "coverage_percentage": 87.5, "issues": [{"file_path": "services/api/src/routes/v1/admin.py", "line_number": 163, "element_type": "function", "element_name": "restart_task", "issue_description": "Missing docstring", "severity": "high"}], "has_module_docstring": true}, {"file_path": "services/monitoring/fibratus/file_tree_monitor.py", "total_elements": 8, "documented_elements": 7, "coverage_percentage": 87.5, "issues": [{"file_path": "services/monitoring/fibratus/file_tree_monitor.py", "line_number": 17, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}], "has_module_docstring": true}, {"file_path": "services/monitoring/fibratus/registry_monitor.py", "total_elements": 8, "documented_elements": 7, "coverage_percentage": 87.5, "issues": [{"file_path": "services/monitoring/fibratus/registry_monitor.py", "line_number": 15, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}], "has_module_docstring": true}, {"file_path": "api/services/elk_logger.py", "total_elements": 9, "documented_elements": 8, "coverage_percentage": 88.88888888888889, "issues": [{"file_path": "api/services/elk_logger.py", "line_number": 320, "element_type": "method", "element_name": "close", "issue_description": "Single-line docstring too brief", "severity": "high"}], "has_module_docstring": true}, {"file_path": "services/workers/tasks/vm_agent_injector.py", "total_elements": 9, "documented_elements": 8, "coverage_percentage": 88.88888888888889, "issues": [{"file_path": "services/workers/tasks/vm_agent_injector.py", "line_number": 29, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}], "has_module_docstring": true}, {"file_path": "services/workers/tasks/vm_pool_manager.py", "total_elements": 9, "documented_elements": 8, "coverage_percentage": 88.88888888888889, "issues": [{"file_path": "services/workers/tasks/vm_pool_manager.py", "line_number": 33, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}], "has_module_docstring": true}, {"file_path": "tests/performance/run_benchmarks.py", "total_elements": 9, "documented_elements": 8, "coverage_percentage": 88.88888888888889, "issues": [{"file_path": "tests/performance/run_benchmarks.py", "line_number": 23, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}], "has_module_docstring": true}, {"file_path": "scripts/collect-ecs-data.py", "total_elements": 10, "documented_elements": 9, "coverage_percentage": 90.0, "issues": [{"file_path": "scripts/collect-ecs-data.py", "line_number": 19, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}], "has_module_docstring": true}, {"file_path": "scripts/dependency-status.py", "total_elements": 10, "documented_elements": 9, "coverage_percentage": 90.0, "issues": [{"file_path": "scripts/dependency-status.py", "line_number": 20, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}], "has_module_docstring": true}, {"file_path": "scripts/health-check-manager.py", "total_elements": 20, "documented_elements": 18, "coverage_percentage": 90.0, "issues": [{"file_path": "scripts/health-check-manager.py", "line_number": 447, "element_type": "function", "element_name": "get_level", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "scripts/health-check-manager.py", "line_number": 319, "element_type": "function", "element_name": "check_recursive", "issue_description": "Missing docstring", "severity": "high"}], "has_module_docstring": true}, {"file_path": "scripts/run-malware-analysis.py", "total_elements": 10, "documented_elements": 9, "coverage_percentage": 90.0, "issues": [{"file_path": "scripts/run-malware-analysis.py", "line_number": 49, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}], "has_module_docstring": true}, {"file_path": "services/api/src/config/vagrant.py", "total_elements": 10, "documented_elements": 9, "coverage_percentage": 90.0, "issues": [{"file_path": "services/api/src/config/vagrant.py", "line_number": 16, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}], "has_module_docstring": true}, {"file_path": "services/monitoring/fibratus/monitor.py", "total_elements": 20, "documented_elements": 18, "coverage_percentage": 90.0, "issues": [{"file_path": "services/monitoring/fibratus/monitor.py", "line_number": 47, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "services/monitoring/fibratus/monitor.py", "line_number": 489, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}], "has_module_docstring": true}, {"file_path": "services/workers/tasks/elk_integration.py", "total_elements": 10, "documented_elements": 9, "coverage_percentage": 90.0, "issues": [{"file_path": "services/workers/tasks/elk_integration.py", "line_number": 33, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}], "has_module_docstring": true}, {"file_path": "scripts/generate-generic-report.py", "total_elements": 11, "documented_elements": 10, "coverage_percentage": 90.9090909090909, "issues": [{"file_path": "scripts/generate-generic-report.py", "line_number": 18, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}], "has_module_docstring": true}, {"file_path": "scripts/generate-sphinx-10-binary-report.py", "total_elements": 11, "documented_elements": 10, "coverage_percentage": 90.9090909090909, "issues": [{"file_path": "scripts/generate-sphinx-10-binary-report.py", "line_number": 19, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}], "has_module_docstring": true}, {"file_path": "scripts/test-vm-websockets.py", "total_elements": 11, "documented_elements": 10, "coverage_percentage": 90.9090909090909, "issues": [{"file_path": "scripts/test-vm-websockets.py", "line_number": 367, "element_type": "method", "element_name": "run_all_tests", "issue_description": "Contains placeholder text: 'pass'", "severity": "high"}], "has_module_docstring": true}, {"file_path": "services/api/src/routes/health.py", "total_elements": 11, "documented_elements": 10, "coverage_percentage": 90.9090909090909, "issues": [{"file_path": "services/api/src/routes/health.py", "line_number": 21, "element_type": "function", "element_name": "health_check", "issue_description": "Single-line docstring too brief", "severity": "high"}], "has_module_docstring": true}, {"file_path": "tests/integration/docker_vm_manager.py", "total_elements": 12, "documented_elements": 11, "coverage_percentage": 91.66666666666666, "issues": [{"file_path": "tests/integration/docker_vm_manager.py", "line_number": 16, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}], "has_module_docstring": true}, {"file_path": "api/services/vm_metrics_service.py", "total_elements": 13, "documented_elements": 12, "coverage_percentage": 92.3076923076923, "issues": [{"file_path": "api/services/vm_metrics_service.py", "line_number": 20, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}], "has_module_docstring": true}, {"file_path": "scripts/check-python-dependencies.py", "total_elements": 13, "documented_elements": 12, "coverage_percentage": 92.3076923076923, "issues": [{"file_path": "scripts/check-python-dependencies.py", "line_number": 23, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}], "has_module_docstring": true}, {"file_path": "scripts/generate-simple-sphinx-report.py", "total_elements": 13, "documented_elements": 12, "coverage_percentage": 92.3076923076923, "issues": [{"file_path": "scripts/generate-simple-sphinx-report.py", "line_number": 17, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}], "has_module_docstring": true}, {"file_path": "scripts/docstring_analyzer.py", "total_elements": 13, "documented_elements": 12, "coverage_percentage": 92.3076923076923, "issues": [{"file_path": "scripts/docstring_analyzer.py", "line_number": 52, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}], "has_module_docstring": true}, {"file_path": "scripts/run-single-binary-rich-cli.py", "total_elements": 14, "documented_elements": 13, "coverage_percentage": 92.85714285714286, "issues": [{"file_path": "scripts/run-single-binary-rich-cli.py", "line_number": 28, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}], "has_module_docstring": true}, {"file_path": "services/api/src/routes/v1/vms.py", "total_elements": 24, "documented_elements": 23, "coverage_percentage": 95.83333333333334, "issues": [{"file_path": "services/api/src/routes/v1/vms.py", "line_number": 257, "element_type": "class", "element_name": "VMResponse", "issue_description": "Single-line docstring too brief", "severity": "high"}], "has_module_docstring": true}, {"file_path": "api/v1/routes/reporting.py", "total_elements": 30, "documented_elements": 29, "coverage_percentage": 96.66666666666667, "issues": [{"file_path": "api/v1/routes/reporting.py", "line_number": 156, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}], "has_module_docstring": true}, {"file_path": "tests/features/steps/file_injection_steps.py", "total_elements": 53, "documented_elements": 52, "coverage_percentage": 98.11320754716981, "issues": [{"file_path": "tests/features/steps/file_injection_steps.py", "line_number": 401, "element_type": "function", "element_name": "upload_file", "issue_description": "Missing docstring", "severity": "high"}], "has_module_docstring": true}, {"file_path": "scripts/turdparty-cli.py", "total_elements": 59, "documented_elements": 58, "coverage_percentage": 98.30508474576271, "issues": [{"file_path": "scripts/turdparty-cli.py", "line_number": 1557, "element_type": "function", "element_name": "logs", "issue_description": "Single-line docstring too brief", "severity": "high"}], "has_module_docstring": true}, {"file_path": "main.py", "total_elements": 1, "documented_elements": 1, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "api/__init__.py", "total_elements": 1, "documented_elements": 1, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "api/exceptions/api_exceptions.py", "total_elements": 29, "documented_elements": 29, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "api/middleware/exception_handlers.py", "total_elements": 10, "documented_elements": 10, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "api/models/__init__.py", "total_elements": 1, "documented_elements": 1, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "api/models/error_responses.py", "total_elements": 7, "documented_elements": 7, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "api/models/file_injection.py", "total_elements": 8, "documented_elements": 8, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "api/services/__init__.py", "total_elements": 1, "documented_elements": 1, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "api/services/file_injection_service.py", "total_elements": 10, "documented_elements": 10, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "api/services/vm_service.py", "total_elements": 17, "documented_elements": 17, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "api/v1/__init__.py", "total_elements": 1, "documented_elements": 1, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "api/v1/application.py", "total_elements": 13, "documented_elements": 13, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "api/v1/routes/__init__.py", "total_elements": 1, "documented_elements": 1, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "api/v1/routes/file_injection.py", "total_elements": 7, "documented_elements": 7, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "api/v1/routes/files.py", "total_elements": 8, "documented_elements": 8, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "api/v1/routes/template_injection.py", "total_elements": 8, "documented_elements": 8, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "api/v1/routes/vm_injection.py", "total_elements": 9, "documented_elements": 9, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "api/v1/routes/vm_management.py", "total_elements": 18, "documented_elements": 18, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "development/examples/main.py", "total_elements": 2, "documented_elements": 2, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "development/tools/debug_vm_creation.py", "total_elements": 7, "documented_elements": 7, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "frontend/backend/app/main.py", "total_elements": 1, "documented_elements": 1, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "frontend/scripts/capture_screenshots.py", "total_elements": 3, "documented_elements": 3, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "scripts/demo-notepadpp-workflow.py", "total_elements": 7, "documented_elements": 7, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "scripts/demo-windows-dev-workflow.py", "total_elements": 9, "documented_elements": 9, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "scripts/fix-sphinx-rst-formatting.py", "total_elements": 4, "documented_elements": 4, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "scripts/generate-git-report.py", "total_elements": 4, "documented_elements": 4, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "scripts/generate-notepadpp-report.py", "total_elements": 9, "documented_elements": 9, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "scripts/generate-sphinx-reports.py", "total_elements": 2, "documented_elements": 2, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "scripts/test-circular-deps.py", "total_elements": 2, "documented_elements": 2, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "scripts/test-top-10-binaries.py", "total_elements": 8, "documented_elements": 8, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "scripts/test-workflow-simple.py", "total_elements": 7, "documented_elements": 7, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "scripts/generate_docstring_report.py", "total_elements": 5, "documented_elements": 5, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "services/api/alembic/env.py", "total_elements": 3, "documented_elements": 3, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "services/api/src/__init__.py", "total_elements": 1, "documented_elements": 1, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "services/api/src/main.py", "total_elements": 10, "documented_elements": 10, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "services/api/src/models/__init__.py", "total_elements": 1, "documented_elements": 1, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "services/api/src/models/base.py", "total_elements": 4, "documented_elements": 4, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "services/api/src/models/file_upload.py", "total_elements": 4, "documented_elements": 4, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "services/api/src/models/vm_instance.py", "total_elements": 6, "documented_elements": 6, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "services/api/src/models/workflow_job.py", "total_elements": 6, "documented_elements": 6, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "services/api/src/routes/__init__.py", "total_elements": 1, "documented_elements": 1, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "services/api/src/routes/celery_health.py", "total_elements": 6, "documented_elements": 6, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "services/api/src/routes/v1/__init__.py", "total_elements": 1, "documented_elements": 1, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "services/api/src/routes/v1/ecs.py", "total_elements": 7, "documented_elements": 7, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "services/api/src/routes/v1/files.py", "total_elements": 5, "documented_elements": 5, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "services/api/src/routes/v1/workflow.py", "total_elements": 4, "documented_elements": 4, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "services/api/src/services/__init__.py", "total_elements": 1, "documented_elements": 1, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "services/api/src/services/celery_app.py", "total_elements": 3, "documented_elements": 3, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "services/api/src/services/database.py", "total_elements": 4, "documented_elements": 4, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "services/api/src/services/minio_client.py", "total_elements": 7, "documented_elements": 7, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "services/workers/celery_app.py", "total_elements": 1, "documented_elements": 1, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "services/workers/tasks/__init__.py", "total_elements": 1, "documented_elements": 1, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "services/workers/tasks/file_operations.py", "total_elements": 3, "documented_elements": 3, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "services/workers/tasks/injection_tasks.py", "total_elements": 7, "documented_elements": 7, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "services/workers/tasks/models.py", "total_elements": 3, "documented_elements": 3, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "services/workers/tasks/scheduled_maintenance.py", "total_elements": 9, "documented_elements": 9, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "services/workers/tasks/simple_elk_ops.py", "total_elements": 3, "documented_elements": 3, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "services/workers/tasks/simple_file_ops.py", "total_elements": 3, "documented_elements": 3, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "services/workers/tasks/simple_vm_ops.py", "total_elements": 6, "documented_elements": 6, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "services/workers/tasks/simple_workflow.py", "total_elements": 3, "documented_elements": 3, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "services/workers/tasks/task_monitoring.py", "total_elements": 12, "documented_elements": 12, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "services/workers/tasks/vm_management.py", "total_elements": 13, "documented_elements": 13, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "services/workers/tasks/workflow_orchestrator.py", "total_elements": 8, "documented_elements": 8, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "tests/__init__.py", "total_elements": 1, "documented_elements": 1, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "tests/e2e/__init__.py", "total_elements": 1, "documented_elements": 1, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "tests/features/environment.py", "total_elements": 9, "documented_elements": 9, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "tests/load/locustfile.py", "total_elements": 25, "documented_elements": 25, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "tests/unit/__init__.py", "total_elements": 1, "documented_elements": 1, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}, {"file_path": "utils/service_urls.py", "total_elements": 17, "documented_elements": 17, "coverage_percentage": 100.0, "issues": [], "has_module_docstring": true}]}, "all_issues": [{"file_path": "api/middleware/ecs_logging.py", "line_number": 233, "element_type": "class", "element_name": "ECSFormatter", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "api/middleware/ecs_logging.py", "line_number": 234, "element_type": "method", "element_name": "format", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "api/middleware/ecs_logging.py", "line_number": 438, "element_type": "class", "element_name": "ECSFormatter", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "api/middleware/ecs_logging.py", "line_number": 439, "element_type": "method", "element_name": "format", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "services/api/src/utils/logging.py", "line_number": 1, "element_type": "module", "element_name": "<module>", "issue_description": "Contains placeholder text: 'pass'", "severity": "high"}, {"file_path": "services/api/src/utils/logging.py", "line_number": 173, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "services/api/src/utils/logging.py", "line_number": 209, "element_type": "function", "element_name": "send_wrapper", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "services/api/src/utils/logging.py", "line_number": 272, "element_type": "function", "element_name": "decorator", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "services/api/src/utils/logging.py", "line_number": 274, "element_type": "function", "element_name": "async_wrapper", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "services/api/src/utils/logging.py", "line_number": 296, "element_type": "function", "element_name": "sync_wrapper", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "scripts/test-service-urls.py", "line_number": 67, "element_type": "function", "element_name": "test_service_url_manager", "issue_description": "Contains placeholder text: 'pass'", "severity": "high"}, {"file_path": "docker/playwright/health_server.py", "line_number": 143, "element_type": "method", "element_name": "serve_report_file", "issue_description": "Single-line docstring too brief", "severity": "high"}, {"file_path": "docker/playwright/health_server.py", "line_number": 165, "element_type": "method", "element_name": "send_404", "issue_description": "Single-line docstring too brief", "severity": "high"}, {"file_path": "scripts/run-5-binary-analysis.py", "line_number": 305, "element_type": "function", "element_name": "main", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "scripts/run-5-binary-analysis.py", "line_number": 20, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "scripts/test-windows-dev-binaries.py", "line_number": 71, "element_type": "class", "element_name": "WindowsDevBinaryTester", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "scripts/test-windows-dev-binaries.py", "line_number": 503, "element_type": "function", "element_name": "main", "issue_description": "Single-line docstring too brief", "severity": "high"}, {"file_path": "scripts/test-windows-dev-binaries.py", "line_number": 72, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "services/api/src/services/vm_manager.py", "line_number": 19, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "services/api/src/services/vm_manager.py", "line_number": 178, "element_type": "method", "element_name": "stop_vm", "issue_description": "Single-line docstring too brief", "severity": "high"}, {"file_path": "services/api/src/services/vm_manager.py", "line_number": 348, "element_type": "method", "element_name": "_start_vagrant_vm", "issue_description": "Single-line docstring too brief", "severity": "medium"}, {"file_path": "services/api/src/services/vm_manager.py", "line_number": 359, "element_type": "method", "element_name": "_stop_vagrant_vm", "issue_description": "Single-line docstring too brief", "severity": "medium"}, {"file_path": "api/models/vm_management.py", "line_number": 9, "element_type": "class", "element_name": "VMType", "issue_description": "Single-line docstring too brief", "severity": "high"}, {"file_path": "api/models/vm_management.py", "line_number": 15, "element_type": "class", "element_name": "VMStatus", "issue_description": "Single-line docstring too brief", "severity": "high"}, {"file_path": "api/models/vm_management.py", "line_number": 86, "element_type": "class", "element_name": "VMMetrics", "issue_description": "Single-line docstring too brief", "severity": "high"}, {"file_path": "api/models/vm_management.py", "line_number": 163, "element_type": "class", "element_name": "ProcessInfo", "issue_description": "Single-line docstring too brief", "severity": "high"}, {"file_path": "api/models/vm_management.py", "line_number": 171, "element_type": "class", "element_name": "NetworkStats", "issue_description": "Single-line docstring too brief", "severity": "high"}, {"file_path": "tests/integration/run_integration_tests.py", "line_number": 373, "element_type": "function", "element_name": "main", "issue_description": "Single-line docstring too brief", "severity": "high"}, {"file_path": "tests/integration/run_integration_tests.py", "line_number": 34, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "api/v1/routes/vm_files.py", "line_number": 436, "element_type": "function", "element_name": "delete_vm_file", "issue_description": "Single-line docstring too brief", "severity": "high"}, {"file_path": "api/v1/routes/vm_files.py", "line_number": 83, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "scripts/generate-coverage-badge.py", "line_number": 97, "element_type": "function", "element_name": "main", "issue_description": "Single-line docstring too brief", "severity": "high"}, {"file_path": "scripts/run-notepadpp-celery-workflow.py", "line_number": 318, "element_type": "function", "element_name": "main", "issue_description": "Single-line docstring too brief", "severity": "high"}, {"file_path": "scripts/run-notepadpp-celery-workflow.py", "line_number": 25, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "services/report_generator.py", "line_number": 387, "element_type": "function", "element_name": "main", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "services/report_generator.py", "line_number": 20, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "services/report_generator.py", "line_number": 381, "element_type": "method", "element_name": "cleanup", "issue_description": "Single-line docstring too brief", "severity": "high"}, {"file_path": "scripts/run-10-binaries-rich-cli.py", "line_number": 32, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "scripts/run-10-binaries-rich-cli.py", "line_number": 155, "element_type": "method", "element_name": "print_header", "issue_description": "Single-line docstring too brief", "severity": "high"}, {"file_path": "scripts/run-notepadpp-workflow.py", "line_number": 470, "element_type": "function", "element_name": "main", "issue_description": "Single-line docstring too brief", "severity": "high"}, {"file_path": "scripts/run-notepadpp-workflow.py", "line_number": 24, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "scripts/test-end-to-end-workflow.py", "line_number": 447, "element_type": "function", "element_name": "main", "issue_description": "Single-line docstring too brief", "severity": "high"}, {"file_path": "scripts/test-end-to-end-workflow.py", "line_number": 25, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "scripts/test-reporting-api.py", "line_number": 342, "element_type": "function", "element_name": "main", "issue_description": "Single-line docstring too brief", "severity": "high"}, {"file_path": "scripts/test-reporting-api.py", "line_number": 23, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "services/workers/vm_manager.py", "line_number": 19, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "services/workers/vm_manager.py", "line_number": 178, "element_type": "method", "element_name": "stop_vm", "issue_description": "Single-line docstring too brief", "severity": "high"}, {"file_path": "services/monitoring/vm-agent/agent.py", "line_number": 410, "element_type": "function", "element_name": "main", "issue_description": "Single-line docstring too brief", "severity": "high"}, {"file_path": "services/monitoring/vm-agent/agent.py", "line_number": 199, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "services/monitoring/vm-agent/agent.py", "line_number": 244, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "services/monitoring/vm-agent/agent.py", "line_number": 326, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "scripts/download-binaries.py", "line_number": 18, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "scripts/generate-license-docs.py", "line_number": 515, "element_type": "function", "element_name": "main", "issue_description": "Single-line docstring too brief", "severity": "high"}, {"file_path": "scripts/generate-license-docs.py", "line_number": 21, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "scripts/mock-replacement-tracker.py", "line_number": 294, "element_type": "function", "element_name": "main", "issue_description": "Single-line docstring too brief", "severity": "high"}, {"file_path": "scripts/mock-replacement-tracker.py", "line_number": 21, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "scripts/test-health-checks.py", "line_number": 1, "element_type": "module", "element_name": "<module>", "issue_description": "Contains placeholder text: 'pass'", "severity": "high"}, {"file_path": "scripts/test-health-checks.py", "line_number": 127, "element_type": "method", "element_name": "run_all_tests", "issue_description": "Contains placeholder text: 'pass'", "severity": "high"}, {"file_path": "scripts/test-vagrant-grpc-connectivity.py", "line_number": 152, "element_type": "function", "element_name": "main", "issue_description": "Single-line docstring too brief", "severity": "high"}, {"file_path": "services/monitoring/fibratus/comprehensive_monitor.py", "line_number": 18, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "services/workers/src/models/vm_instance.py", "line_number": 89, "element_type": "method", "element_name": "__repr__", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "scripts/inject-files.py", "line_number": 19, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "services/api/src/routes/v1/admin.py", "line_number": 163, "element_type": "function", "element_name": "restart_task", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "services/monitoring/fibratus/file_tree_monitor.py", "line_number": 17, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "services/monitoring/fibratus/registry_monitor.py", "line_number": 15, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "api/services/elk_logger.py", "line_number": 320, "element_type": "method", "element_name": "close", "issue_description": "Single-line docstring too brief", "severity": "high"}, {"file_path": "services/workers/tasks/vm_agent_injector.py", "line_number": 29, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "services/workers/tasks/vm_pool_manager.py", "line_number": 33, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "tests/performance/run_benchmarks.py", "line_number": 23, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "scripts/collect-ecs-data.py", "line_number": 19, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "scripts/dependency-status.py", "line_number": 20, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "scripts/health-check-manager.py", "line_number": 447, "element_type": "function", "element_name": "get_level", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "scripts/health-check-manager.py", "line_number": 319, "element_type": "function", "element_name": "check_recursive", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "scripts/run-malware-analysis.py", "line_number": 49, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "services/api/src/config/vagrant.py", "line_number": 16, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "services/monitoring/fibratus/monitor.py", "line_number": 47, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "services/monitoring/fibratus/monitor.py", "line_number": 489, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "services/workers/tasks/elk_integration.py", "line_number": 33, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "scripts/generate-generic-report.py", "line_number": 18, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "scripts/generate-sphinx-10-binary-report.py", "line_number": 19, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "scripts/test-vm-websockets.py", "line_number": 367, "element_type": "method", "element_name": "run_all_tests", "issue_description": "Contains placeholder text: 'pass'", "severity": "high"}, {"file_path": "services/api/src/routes/health.py", "line_number": 21, "element_type": "function", "element_name": "health_check", "issue_description": "Single-line docstring too brief", "severity": "high"}, {"file_path": "tests/integration/docker_vm_manager.py", "line_number": 16, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "api/services/vm_metrics_service.py", "line_number": 20, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "scripts/check-python-dependencies.py", "line_number": 23, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "scripts/generate-simple-sphinx-report.py", "line_number": 17, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "scripts/docstring_analyzer.py", "line_number": 52, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "scripts/run-single-binary-rich-cli.py", "line_number": 28, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "services/api/src/routes/v1/vms.py", "line_number": 257, "element_type": "class", "element_name": "VMResponse", "issue_description": "Single-line docstring too brief", "severity": "high"}, {"file_path": "api/v1/routes/reporting.py", "line_number": 156, "element_type": "method", "element_name": "__init__", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "tests/features/steps/file_injection_steps.py", "line_number": 401, "element_type": "function", "element_name": "upload_file", "issue_description": "Missing docstring", "severity": "high"}, {"file_path": "scripts/turdparty-cli.py", "line_number": 1557, "element_type": "function", "element_name": "logs", "issue_description": "Single-line docstring too brief", "severity": "high"}]}