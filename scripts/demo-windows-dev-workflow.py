#!/usr/bin/env python3
"""
TurdParty Windows Developer Binaries Workflow Demo

This script demonstrates the complete TurdParty malware analysis workflow using
popular Windows developer tools and applications. It simulates the entire pipeline
from file upload through VM execution to result collection and reporting.

Key Features:
    - Simulates complete API workflow for 5 popular Windows developer tools
    - Generates realistic file metadata including hashes and sizes
    - Simulates MinIO file storage operations
    - Simulates VM creation, file injection, and execution
    - Generates ECS-compliant log entries for monitoring
    - Produces comprehensive workflow reports with performance metrics

Developer Applications Tested:
    1. Visual Studio Code - Popular code editor
    2. Python - Programming language runtime
    3. Git - Version control system
    4. Node.js - JavaScript runtime
    5. Notepad++ - Advanced text editor

Workflow Simulation:
    1. File metadata generation with realistic hashes
    2. MinIO upload simulation with timing
    3. Windows VM creation (gusztavvargadr/windows-10)
    4. File injection into VM environment
    5. Silent installation execution
    6. Filesystem and registry change tracking
    7. ECS log generation for monitoring
    8. Performance metrics collection

Output:
    - Real-time progress display
    - Comprehensive JSON report with all workflow data
    - Performance metrics and timing analysis
    - ECS log entries for each workflow stage
    - Success/failure tracking and reporting

Usage:
    python scripts/demo-windows-dev-workflow.py

    Generates report: /tmp/windows-dev-workflow-demo-{timestamp}.json

Integration:
    This demo script validates the TurdParty API workflow design and provides
    realistic test data for development and testing purposes.
"""

import asyncio
import json
import time
import uuid
from datetime import datetime
from pathlib import Path
import hashlib
import sys
import os

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

# Top 5 Windows Developer Binaries
DEVELOPER_BINARIES = [
    {
        "name": "vscode",
        "description": "Visual Studio Code",
        "url": "https://code.visualstudio.com/sha/download?build=stable&os=win32-x64",
        "filename": "VSCodeSetup-x64.exe",
        "type": "msi",
        "install_command": "VSCodeSetup-x64.exe /VERYSILENT /NORESTART /MERGETASKS=!runcode"
    },
    {
        "name": "python",
        "description": "Python Programming Language",
        "url": "https://www.python.org/ftp/python/3.12.1/python-3.12.1-amd64.exe",
        "filename": "python-3.12.1-amd64.exe",
        "type": "msi",
        "install_command": "python-3.12.1-amd64.exe /quiet InstallAllUsers=1 PrependPath=1"
    },
    {
        "name": "git",
        "description": "Git Version Control System",
        "url": "https://github.com/git-for-windows/git/releases/download/v2.42.0.windows.2/Git-********-64-bit.exe",
        "filename": "Git-********-64-bit.exe",
        "type": "msi",
        "install_command": "Git-********-64-bit.exe /VERYSILENT /NORESTART"
    },
    {
        "name": "nodejs",
        "description": "Node.js JavaScript Runtime",
        "url": "https://nodejs.org/dist/v20.10.0/node-v20.10.0-x64.msi",
        "filename": "node-v20.10.0-x64.msi",
        "type": "msi",
        "install_command": "msiexec /i node-v20.10.0-x64.msi /quiet /norestart"
    },
    {
        "name": "notepadpp",
        "description": "Notepad++ Text Editor",
        "url": "https://github.com/notepad-plus-plus/notepad-plus-plus/releases/download/v8.5.8/npp.8.5.8.Installer.x64.exe",
        "filename": "npp.8.5.8.Installer.x64.exe",
        "type": "exe",
        "install_command": "npp.8.5.8.Installer.x64.exe /S"
    }
]

class WindowsDevWorkflowDemo:
    """
    Windows Developer Binaries Workflow Demo Class.

    Orchestrates the complete TurdParty workflow simulation for Windows
    developer tools, including file processing, VM operations, and reporting.
    """

    def __init__(self):
        """
        Initialize the workflow demo.

        Sets up test tracking and timing for comprehensive reporting.
        """
        self.test_results = []
        self.test_start_time = datetime.now()
        
    def generate_file_metadata(self, file_path: Path, app: dict) -> dict:
        """
        Generate comprehensive file metadata for workflow simulation.

        Creates realistic file metadata including hashes, sizes, and application
        information for use in the TurdParty workflow simulation.

        Args:
            file_path: Path to the file (may not exist for simulation)
            app: Application dictionary with name, description, and install info

        Returns:
            dict: Complete file metadata including UUID, hashes, and app info
        """
        file_uuid = str(uuid.uuid4())
        file_size = file_path.stat().st_size if file_path.exists() else 0
        
        # Calculate hashes if file exists
        if file_path.exists():
            # SHA256 hash
            sha256_hash = hashlib.sha256()
            with file_path.open('rb') as f:
                for chunk in iter(lambda: f.read(8192), b""):
                    sha256_hash.update(chunk)
            sha256_hex = sha256_hash.hexdigest()
            
            # Simulate Blake3 hash (would use blake3 library in real implementation)
            blake3_hex = f"blake3_{sha256_hex[:32]}"
        else:
            sha256_hex = f"simulated_sha256_{int(time.time())}"
            blake3_hex = f"simulated_blake3_{int(time.time())}"
        
        metadata = {
            'file_uuid': file_uuid,
            'filename': app['filename'],
            'file_size': file_size,
            'sha256_hash': sha256_hex,
            'blake3_hash': blake3_hex,
            'download_url': app['url'],
            'application_name': app['name'],
            'application_description': app['description'],
            'file_type': app['type'],
            'platform': 'windows',
            'timestamp': datetime.now().isoformat(),
            'install_command': app['install_command']
        }
        
        return metadata

    def simulate_api_workflow(self, metadata: dict) -> dict:
        """
        Simulate the complete TurdParty API workflow.

        Creates realistic simulation of all workflow stages including MinIO upload,
        VM creation, file injection, execution, and ECS logging.

        Args:
            metadata: File metadata dictionary from generate_file_metadata

        Returns:
            dict: Complete workflow results including all stage outputs
        """
        
        # Simulate MinIO upload
        minio_result = {
            'bucket': 'turdparty-uploads',
            'object_name': f"{metadata['file_uuid']}/{metadata['filename']}",
            'file_id': f"file_{metadata['file_uuid'][:8]}",
            'upload_time_seconds': round(metadata['file_size'] / 1000000, 2),  # Simulate based on file size
            'etag': f"etag_{int(time.time())}",
            'version_id': f"v_{int(time.time())}"
        }
        
        # Simulate VM creation
        vm_result = {
            'vm_id': f"vm_{uuid.uuid4().hex[:8]}",
            'vm_name': f"dev-{metadata['application_name']}-{int(time.time())}",
            'template': 'gusztavvargadr/windows-10',
            'status': 'running',
            'boot_time_seconds': 45.7,  # Typical Windows VM boot time
            'memory_mb': 4096,
            'cpus': 2,
            'created_at': datetime.now().isoformat()
        }
        
        # Simulate file injection
        injection_result = {
            'injection_id': f"inj_{uuid.uuid4().hex[:8]}",
            'target_path': f"C:\\temp\\{metadata['filename']}",
            'injection_time_seconds': 2.3,
            'success': True,
            'file_size_injected': metadata['file_size']
        }
        
        # Simulate installation execution
        execution_result = {
            'execution_id': f"exec_{uuid.uuid4().hex[:8]}",
            'command': metadata['install_command'],
            'exit_code': 0,
            'execution_time_seconds': 15.8,  # Typical installation time
            'success': True,
            'stdout_lines': 12,
            'stderr_lines': 0,
            'filesystem_changes': {
                'files_created': 156,
                'directories_created': 23,
                'registry_keys_modified': 45,
                'services_installed': 2
            }
        }
        
        # Simulate ECS log entries
        ecs_logs = [
            {
                '@timestamp': datetime.now().isoformat(),
                'log.level': 'INFO',
                'message': f"File uploaded to MinIO: {metadata['filename']}",
                'file_uuid': metadata['file_uuid'],
                'service.name': 'turdparty-api',
                'event.action': 'file_upload',
                'file.size': metadata['file_size'],
                'file.hash.blake3': metadata['blake3_hash']
            },
            {
                '@timestamp': datetime.now().isoformat(),
                'log.level': 'INFO',
                'message': f"VM created for processing: {vm_result['vm_id']}",
                'file_uuid': metadata['file_uuid'],
                'vm_id': vm_result['vm_id'],
                'service.name': 'turdparty-vm-manager',
                'event.action': 'vm_creation'
            },
            {
                '@timestamp': datetime.now().isoformat(),
                'log.level': 'INFO',
                'message': f"File injected into VM: {injection_result['target_path']}",
                'file_uuid': metadata['file_uuid'],
                'vm_id': vm_result['vm_id'],
                'injection_id': injection_result['injection_id'],
                'service.name': 'turdparty-vm-manager',
                'event.action': 'file_injection'
            },
            {
                '@timestamp': datetime.now().isoformat(),
                'log.level': 'INFO',
                'message': f"Application installed successfully: {metadata['application_name']}",
                'file_uuid': metadata['file_uuid'],
                'vm_id': vm_result['vm_id'],
                'execution_id': execution_result['execution_id'],
                'service.name': 'turdparty-vm-manager',
                'event.action': 'application_installation',
                'exit_code': execution_result['exit_code']
            }
        ]
        
        return {
            'minio_result': minio_result,
            'vm_result': vm_result,
            'injection_result': injection_result,
            'execution_result': execution_result,
            'ecs_logs': ecs_logs
        }

    def process_single_binary(self, app: dict) -> dict:
        """
        Process a single binary through the complete workflow.

        Executes the full TurdParty workflow simulation for one application,
        including metadata generation, API workflow simulation, and result tracking.

        Args:
            app: Application dictionary with name, description, and install info

        Returns:
            dict: Complete processing results including timing and success status
        """
        print(f"\n{'='*60}")
        print(f"🧪 Processing {app['name']} - {app['description']}")
        print(f"{'='*60}")
        
        test_start = time.time()
        
        # Step 1: Generate file metadata (simulating download)
        download_dir = Path("/tmp/turdparty-dev-binaries")
        download_dir.mkdir(exist_ok=True)
        file_path = download_dir / app['filename']
        
        print(f"📥 Simulating download of {app['filename']}...")
        metadata = self.generate_file_metadata(file_path, app)
        print(f"✅ Generated metadata - UUID: {metadata['file_uuid']}")
        print(f"   Blake3: {metadata['blake3_hash'][:16]}...")
        print(f"   File size: {metadata['file_size']:,} bytes")
        
        # Step 2: Simulate complete API workflow
        print(f"🔄 Simulating TurdParty API workflow...")
        workflow_result = self.simulate_api_workflow(metadata)
        
        # Step 3: Display workflow results
        minio = workflow_result['minio_result']
        vm = workflow_result['vm_result']
        injection = workflow_result['injection_result']
        execution = workflow_result['execution_result']
        
        print(f"📤 MinIO Upload: {minio['bucket']}/{minio['object_name']}")
        print(f"   File ID: {minio['file_id']}")
        print(f"   Upload time: {minio['upload_time_seconds']}s")
        
        print(f"🖥️ VM Creation: {vm['vm_id']} ({vm['template']})")
        print(f"   Boot time: {vm['boot_time_seconds']}s")
        print(f"   Status: {vm['status']}")
        
        print(f"💉 File Injection: {injection['target_path']}")
        print(f"   Injection time: {injection['injection_time_seconds']}s")
        print(f"   Success: {injection['success']}")
        
        print(f"⚙️ Installation: {execution['command'][:50]}...")
        print(f"   Exit code: {execution['exit_code']}")
        print(f"   Execution time: {execution['execution_time_seconds']}s")
        print(f"   Files created: {execution['filesystem_changes']['files_created']}")
        print(f"   Registry keys: {execution['filesystem_changes']['registry_keys_modified']}")
        
        total_time = time.time() - test_start
        
        result = {
            'application': app,
            'metadata': metadata,
            'workflow_result': workflow_result,
            'total_time_seconds': total_time,
            'success': execution['success'],
            'timestamp': datetime.now().isoformat()
        }
        
        print(f"✅ {app['name']} processing completed in {total_time:.2f}s")
        
        return result

    def run_all_binaries(self):
        """
        Process all developer binaries through the workflow.

        Executes the complete workflow simulation for all configured
        Windows developer applications and generates a comprehensive report.
        """
        print("🚀 Starting Windows Developer Binaries Workflow Demo")
        print(f"📅 Demo started at: {self.test_start_time}")
        print(f"🎯 Processing {len(DEVELOPER_BINARIES)} applications")
        
        for i, app in enumerate(DEVELOPER_BINARIES, 1):
            print(f"\n🔄 Progress: {i}/{len(DEVELOPER_BINARIES)}")
            result = self.process_single_binary(app)
            self.test_results.append(result)
        
        self.generate_comprehensive_report()

    def generate_comprehensive_report(self):
        """
        Generate comprehensive workflow report.

        Creates detailed JSON report with all workflow results, performance
        metrics, ECS logs, and summary statistics for analysis and validation.
        """
        print(f"\n{'='*80}")
        print("📊 COMPREHENSIVE WORKFLOW REPORT")
        print(f"{'='*80}")
        
        # Save detailed results
        report_file = f"/tmp/windows-dev-workflow-demo-{int(time.time())}.json"
        with open(report_file, 'w') as f:
            json.dump({
                'demo_metadata': {
                    'demo_start_time': self.test_start_time.isoformat(),
                    'demo_end_time': datetime.now().isoformat(),
                    'total_applications': len(DEVELOPER_BINARIES),
                    'workflow_type': 'simulated_api_calls'
                },
                'results': self.test_results
            }, f, indent=2, default=str)
        
        print(f"📄 Detailed report saved: {report_file}")
        
        # Print summary
        successful_tests = [r for r in self.test_results if r['success']]
        
        print(f"\n📈 SUMMARY:")
        print(f"   Total Applications: {len(self.test_results)}")
        print(f"   Successfully Processed: {len(successful_tests)}")
        print(f"   Success Rate: {len(successful_tests)/len(self.test_results)*100:.1f}%")
        
        print(f"\n🔑 MINIO UUIDS & WORKFLOW DATA:")
        for result in self.test_results:
            app_name = result['application']['name']
            uuid_val = result['metadata']['file_uuid']
            file_id = result['workflow_result']['minio_result']['file_id']
            vm_id = result['workflow_result']['vm_result']['vm_id']
            
            print(f"   ✅ {app_name}:")
            print(f"      UUID: {uuid_val}")
            print(f"      File ID: {file_id}")
            print(f"      VM ID: {vm_id}")
            print(f"      Blake3: {result['metadata']['blake3_hash'][:16]}...")
        
        # Display ECS logs summary
        print(f"\n📊 ECS LOGS SUMMARY:")
        total_logs = 0
        for result in self.test_results:
            logs = result['workflow_result']['ecs_logs']
            total_logs += len(logs)
            
            print(f"   {result['application']['name']}: {len(logs)} log entries")
            for log in logs:
                action = log['event.action']
                level = log['log.level']
                print(f"      [{level}] {action}: {log['message'][:60]}...")
        
        print(f"\n📈 PERFORMANCE METRICS:")
        if successful_tests:
            avg_upload = sum(r['workflow_result']['minio_result']['upload_time_seconds'] for r in successful_tests) / len(successful_tests)
            avg_boot = sum(r['workflow_result']['vm_result']['boot_time_seconds'] for r in successful_tests) / len(successful_tests)
            avg_injection = sum(r['workflow_result']['injection_result']['injection_time_seconds'] for r in successful_tests) / len(successful_tests)
            avg_execution = sum(r['workflow_result']['execution_result']['execution_time_seconds'] for r in successful_tests) / len(successful_tests)
            
            print(f"   Average Upload Time: {avg_upload:.2f}s")
            print(f"   Average VM Boot Time: {avg_boot:.2f}s")
            print(f"   Average Injection Time: {avg_injection:.2f}s")
            print(f"   Average Execution Time: {avg_execution:.2f}s")
        
        print(f"\n✅ Windows Developer Binaries Workflow Demo Complete!")
        print(f"📊 Total ECS log entries generated: {total_logs}")
        print(f"🎯 All {len(DEVELOPER_BINARIES)} applications processed successfully")

def main():
    """
    Main entry point for the Windows Developer Binaries Workflow Demo.

    Initializes and runs the complete workflow simulation for all configured
    Windows developer applications, generating comprehensive reports.
    """
    demo = WindowsDevWorkflowDemo()
    demo.run_all_binaries()

if __name__ == "__main__":
    main()
