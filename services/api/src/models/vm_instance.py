"""
Virtual Machine Instance Database Model

This module defines the SQLAlchemy model for VM instances in the TurdParty malware analysis platform.
It tracks virtual machines throughout their lifecycle from creation to termination, including
configuration, status, file injection, monitoring, and workflow integration.

The VMInstance model serves as the central record for all virtual machines used in malware analysis,
providing comprehensive tracking of VM state, resource allocation, and analysis progress.

Key Features:
    - VM lifecycle management with status enumeration
    - Resource configuration tracking (CPU, memory, disk)
    - File injection tracking with foreign key relationships
    - ELK monitoring integration with index tracking
    - Automatic expiration detection (30-minute runtime limit)
    - Runtime calculation for billing and resource management
    - Workflow job association for end-to-end traceability

Database Schema:
    - Primary key: UUID-based unique identifier
    - Foreign keys: injected_file_id, workflow_job_id
    - Indexes: status, started_at, workflow_job_id for efficient querying
    - Constraints: Non-null requirements for essential configuration
    - Relationships: FileUpload for injected files

VM Lifecycle States:
    - CREATING: VM is being provisioned
    - RUNNING: VM is active and ready for operations
    - INJECTING: File injection in progress
    - MONITORING: Analysis monitoring active
    - TERMINATING: VM shutdown in progress
    - TERMINATED: VM has been destroyed
    - FAILED: VM creation or operation failed
"""

import enum
import uuid
from datetime import datetime, timedelta, timezone
from typing import Optional

from sqlalchemy import String, Integer, Text, DateTime, Boolean, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .base import Base


class VMStatus(enum.Enum):
    """VM lifecycle status."""
    CREATING = "creating"
    RUNNING = "running"
    INJECTING = "injecting"
    MONITORING = "monitoring"
    TERMINATING = "terminating"
    TERMINATED = "terminated"
    FAILED = "failed"


class VMInstance(Base):
    """Model for VM instances."""

    __tablename__ = "vm_instances"

    # VM identification
    name: Mapped[str] = mapped_column(String(100), nullable=False)
    vm_id: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)  # External VM ID (Vagrant, etc.)

    # VM configuration
    template: Mapped[str] = mapped_column(String(100), default="ubuntu/focal64", nullable=False)
    memory_mb: Mapped[int] = mapped_column(Integer, default=1024, nullable=False)
    cpus: Mapped[int] = mapped_column(Integer, default=1, nullable=False)
    disk_gb: Mapped[int] = mapped_column(Integer, default=20, nullable=False)

    # VM lifecycle
    status: Mapped[VMStatus] = mapped_column(default=VMStatus.CREATING, nullable=False)
    ip_address: Mapped[Optional[str]] = mapped_column(String(15), nullable=True)
    ssh_port: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)

    # Timing
    started_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    scheduled_termination: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    terminated_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)

    # File injection
    injected_file_id: Mapped[Optional[uuid.UUID]] = mapped_column(UUID(as_uuid=True), ForeignKey("file_uploads.id"), nullable=True)
    injection_path: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    injection_completed: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)

    # Monitoring
    monitoring_active: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    elk_index: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)

    # Error handling
    error_message: Mapped[Optional[str]] = mapped_column(Text, nullable=True)

    # Workflow tracking
    workflow_job_id: Mapped[Optional[uuid.UUID]] = mapped_column(UUID(as_uuid=True), nullable=True)
    
    # Relationships
    injected_file = relationship("FileUpload", foreign_keys=[injected_file_id])
    
    def __repr__(self) -> str:
        """
        Return string representation of the VMInstance.

        Returns:
            str: Human-readable representation showing key identifying information
        """
        return f"<VMInstance(id={self.id}, name={self.name}, status={self.status})>"
    
    @property
    def is_expired(self) -> bool:
        """Check if VM has exceeded its 30-minute runtime."""
        if not self.started_at:
            return False
        return datetime.utcnow() > self.started_at + timedelta(minutes=30)

    @property
    def runtime_minutes(self) -> float:
        """Get current runtime in minutes."""
        if not self.started_at:
            return 0.0
        end_time = self.terminated_at or datetime.utcnow()
        return (end_time - self.started_at).total_seconds() / 60
