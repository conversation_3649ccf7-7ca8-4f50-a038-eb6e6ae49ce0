"""
TurdParty API Service v1 Routes

This module configures and exports the FastAPI router for API version 1.
It aggregates all route handlers from different domains and provides
a unified router for the TurdParty API service.

Route Domains:
    - files: File upload, download, and metadata management
    - workflow: Analysis workflow orchestration and monitoring
    - vms: Virtual machine lifecycle management
    - ecs: ECS event data collection and querying
    - admin: Administrative operations and system management
    - health: Service health checks and status monitoring

Router Configuration:
    - Base prefix: /api/v1
    - Automatic OpenAPI documentation generation
    - Request/response validation using Pydantic models
    - Standardized error handling across all routes
    - ECS-compliant logging for all operations

Features:
    - RESTful API design patterns
    - Async/await for non-blocking operations
    - WebSocket endpoints for real-time updates
    - Comprehensive input validation
    - Structured error responses
    - Rate limiting and authentication (where applicable)

Usage:
    The v1_router is included in the main FastAPI application:

    from services.api.src.routes.v1 import v1_router
    app.include_router(v1_router)

Integration:
    Routes interact with:
    - Database models for data persistence
    - Service layer for business logic
    - Celery workers for background processing
    - External APIs (Vag<PERSON>, Dock<PERSON>, <PERSON><PERSON>, <PERSON>L<PERSON>)
"""

from fastapi import APIRouter
from . import files, workflow, vms, ecs, admin
from .. import health

# Create v1 router
v1_router = APIRouter(prefix="/api/v1")

# Include all v1 routes
v1_router.include_router(files.router, prefix="/files", tags=["files"])
v1_router.include_router(workflow.router, prefix="/workflow", tags=["workflow"])
v1_router.include_router(vms.router, prefix="/vms", tags=["vms"])
v1_router.include_router(ecs.router, prefix="/ecs", tags=["ecs"])
v1_router.include_router(admin.router, prefix="/admin", tags=["admin"])
v1_router.include_router(health.router, prefix="/health", tags=["health"])

__all__ = ["v1_router"]
