"""Administrative endpoints for TurdParty API."""

import logging
import subprocess
import asyncio
from datetime import datetime
from typing import Dict, Any, Optional
from pathlib import Path

from fastapi import APIRouter, HTTPException, BackgroundTasks, status
from pydantic import BaseModel

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/admin", tags=["admin"])

# Project root for script execution
PROJECT_ROOT = Path(__file__).parent.parent.parent.parent.parent.parent


class AdminResponse(BaseModel):
    """Response model for admin operations."""
    success: bool
    message: str
    task_id: Optional[str] = None
    timestamp: str
    details: Optional[Dict[str, Any]] = None


class DocsRebuildRequest(BaseModel):
    """Request model for documentation rebuild."""
    clean: bool = False
    extract_api: bool = True
    restart_frontend: bool = True


async def rebuild_documentation_task(clean: bool = False, extract_api: bool = True, restart_frontend: bool = True):
    """Background task to rebuild documentation."""
    try:
        logger.info("Starting documentation rebuild task")

        # Build documentation
        cmd = ["bash", str(PROJECT_ROOT / "scripts" / "build-docs-auto.sh"), "build"]
        if clean:
            cmd.append("--clean")

        result = await asyncio.create_subprocess_exec(
            *cmd,
            cwd=PROJECT_ROOT,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )

        stdout, stderr = await result.communicate()

        if result.returncode != 0:
            logger.error(f"Documentation build failed: {stderr.decode()}")
            return False

        logger.info("Documentation build completed successfully")

        # Extract API documentation if requested
        if extract_api:
            try:
                import httpx
                async with httpx.AsyncClient() as client:
                    response = await client.get("http://localhost:8000/openapi.json", timeout=10)
                    if response.status_code == 200:
                        api_docs_path = PROJECT_ROOT / "docs" / "_build" / "html" / "api" / "openapi.json"
                        api_docs_path.parent.mkdir(parents=True, exist_ok=True)

                        with open(api_docs_path, 'w') as f:
                            f.write(response.text)

                        logger.info("API documentation extracted successfully")
            except Exception as e:
                logger.warning(f"Failed to extract API documentation: {e}")

        # Restart frontend if requested
        if restart_frontend:
            try:
                restart_cmd = ["docker-compose", "restart", "frontend"]
                restart_result = await asyncio.create_subprocess_exec(
                    *restart_cmd,
                    cwd=PROJECT_ROOT,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )

                await restart_result.communicate()

                if restart_result.returncode == 0:
                    logger.info("Frontend service restarted successfully")
                else:
                    logger.warning("Failed to restart frontend service")

            except Exception as e:
                logger.warning(f"Failed to restart frontend: {e}")

        return True

    except Exception as e:
        logger.error(f"Documentation rebuild task failed: {e}")
        return False


@router.post("/docs/rebuild", response_model=AdminResponse)
async def rebuild_docs(
    request: DocsRebuildRequest,
    background_tasks: BackgroundTasks
):
    """
    Trigger documentation rebuild.

    This endpoint rebuilds the Sphinx documentation, optionally extracts
    API documentation, and restarts the frontend service to serve updated docs.
    """
    try:
        import uuid
        task_id = str(uuid.uuid4())

        # Add background task
        background_tasks.add_task(
            rebuild_documentation_task,
            request.clean,
            request.extract_api,
            request.restart_frontend
        )

        logger.info(f"Documentation rebuild task {task_id} queued")

        return AdminResponse(
            success=True,
            message="Documentation rebuild task queued successfully",
            task_id=task_id,
            timestamp=datetime.utcnow().isoformat(),
            details={
                "clean": request.clean,
                "extract_api": request.extract_api,
                "restart_frontend": request.restart_frontend
            }
        )

    except Exception as e:
        logger.error(f"Failed to queue documentation rebuild: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to queue documentation rebuild: {str(e)}"
        )


@router.post("/services/{service_name}/restart", response_model=AdminResponse)
async def restart_service(service_name: str, background_tasks: BackgroundTasks):
    """
    Restart a specific service.

    Restarts the specified Docker Compose service.
    """
    try:
        import uuid
        task_id = str(uuid.uuid4())

        async def restart_task():
            try:
                result = await asyncio.create_subprocess_exec(
                    "docker-compose", "restart", service_name,
                    cwd=PROJECT_ROOT,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )

                stdout, stderr = await result.communicate()

                if result.returncode == 0:
                    logger.info(f"Service {service_name} restarted successfully")
                else:
                    logger.error(f"Failed to restart {service_name}: {stderr.decode()}")

            except Exception as e:
                logger.error(f"Service restart task failed: {e}")

        background_tasks.add_task(restart_task)

        return AdminResponse(
            success=True,
            message=f"Service {service_name} restart task queued",
            task_id=task_id,
            timestamp=datetime.utcnow().isoformat(),
            details={"service": service_name}
        )

    except Exception as e:
        logger.error(f"Failed to queue service restart: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to queue service restart: {str(e)}"
        )


@router.get("/health")
async def admin_health():
    """Admin endpoint health check."""
    return {
        "status": "healthy",
        "service": "admin-api",
        "timestamp": datetime.utcnow().isoformat()
    }