"""File injection tasks for TurdParty workflow."""

import logging
import os
import subprocess
import tempfile
from typing import Dict, Any
from uuid import UUID
from datetime import datetime

from celery import shared_task
from celery.utils.log import get_task_logger
import sys
from pathlib import Path

# Add project root to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

# Import run tracking utilities (temporarily disabled for debugging)
# from utils.run_tracker import start_file_run, complete_file_run, get_file_runs

# Temporary stub functions
def start_file_run(file_id, workflow_id, vm_id):
    """Temporary stub for run tracking."""
    return 1, f"{file_id}-run-1"

def complete_file_run(file_id, run_number, install_events=0, install_uuid=None):
    """Temporary stub for run tracking."""
    pass

def get_file_runs(file_id):
    """Temporary stub for run tracking."""
    return []

logger = get_task_logger(__name__)


@shared_task(bind=True, max_retries=3, retry_backoff=True)
def inject_file(self, workflow_job_id: str, file_upload_id: str) -> Dict[str, Any]:
    """
    Inject file into running VM.
    
    Args:
        workflow_job_id: ID of the workflow job
        file_upload_id: ID of the file upload record
        
    Returns:
        Dictionary with injection details
    """
    try:
        # Import here to avoid circular imports
        from services.api.src.services.database import SessionLocal
        from services.api.src.services.minio_client import download_file
        from services.api.src.models.workflow_job import WorkflowJob, WorkflowStatus
        from services.api.src.models.vm_instance import VMInstance, VMStatus
        from services.api.src.models.file_upload import FileUpload, FileStatus
        
        db = SessionLocal()
        
        try:
            # Get workflow job
            workflow_job = db.query(WorkflowJob).filter(
                WorkflowJob.id == UUID(workflow_job_id)
            ).first()
            
            if not workflow_job:
                raise ValueError(f"Workflow job not found: {workflow_job_id}")
            
            # Get VM instance
            if not workflow_job.vm_instance_id:
                raise ValueError(f"No VM instance for workflow job: {workflow_job_id}")
            
            vm_instance = db.query(VMInstance).filter(
                VMInstance.id == workflow_job.vm_instance_id
            ).first()
            
            if not vm_instance:
                raise ValueError(f"VM instance not found: {workflow_job.vm_instance_id}")
            
            if vm_instance.status != VMStatus.RUNNING:
                raise ValueError(f"VM not running: {vm_instance.status}")
            
            # Get file upload
            file_upload = db.query(FileUpload).filter(
                FileUpload.id == UUID(file_upload_id)
            ).first()
            
            if not file_upload:
                raise ValueError(f"File upload not found: {file_upload_id}")
            
            logger.info(f"Injecting file {file_upload.filename} into VM {vm_instance.name}")

            # Start new run tracking for this file UUID
            file_id = str(file_upload.id)
            run_number, run_id = start_file_run(file_id, str(workflow_job.id), str(vm_instance.id))
            logger.info(f"Started run #{run_number} (ID: {run_id}) for file {file_upload.filename}")

            # Update status
            workflow_job.status = WorkflowStatus.FILE_INJECTING
            workflow_job.update_progress("file_injection", 60, f"Downloading file for injection (Run #{run_number})")
            vm_instance.status = VMStatus.INJECTING
            db.commit()
            
            # Download file from MinIO (sync version for Celery)
            from services.api.src.services.minio_client import get_minio_client

            client = get_minio_client()
            response = client.get_object(file_upload.minio_bucket, file_upload.minio_object_key)
            file_content = response.read()
            response.close()
            response.release_conn()
            
            # Inject file into VM
            injection_result = _inject_file_into_vm(
                vm_instance, 
                file_content, 
                file_upload.filename,
                workflow_job.injection_config
            )
            
            # Update VM instance
            vm_instance.injected_file_id = file_upload.id
            vm_instance.injection_path = injection_result["target_path"]
            vm_instance.injection_completed = True
            vm_instance.status = VMStatus.MONITORING
            vm_instance.monitoring_active = True
            
            # Update file upload status
            file_upload.status = FileStatus.INJECTED
            
            # Update workflow job
            workflow_job.status = WorkflowStatus.MONITORING
            workflow_job.update_progress("monitoring", 80, "File injected, monitoring started")
            
            if not workflow_job.results:
                workflow_job.results = {}
            
            workflow_job.results.update({
                "injection_completed": True,
                "injection_path": injection_result["target_path"],
                "injection_time": injection_result["injection_time"],
                "file_size": len(file_content),
                "run_number": run_number,
                "run_id": run_id
            })

            db.commit()

            # Start ELK monitoring with run tracking
            install_uuid = _start_elk_monitoring(vm_instance, workflow_job, run_number)

            # Complete run tracking (install phase)
            complete_file_run(
                file_id,
                run_number,
                install_events=1,  # File injection event
                install_uuid=install_uuid
            )

            logger.info(f"File injection completed: {file_upload.filename} -> {injection_result['target_path']} (Run #{run_number})")
            
            return {
                "status": "success",
                "vm_instance_id": str(vm_instance.id),
                "file_upload_id": file_upload_id,
                "injection_path": injection_result["target_path"],
                "file_size": len(file_content),
                "workflow_job_id": workflow_job_id
            }
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"File injection failed: {e}")
        
        # Update workflow job with error
        try:
            db = SessionLocal()
            workflow_job = db.query(WorkflowJob).filter(
                WorkflowJob.id == UUID(workflow_job_id)
            ).first()
            
            if workflow_job:
                workflow_job.status = WorkflowStatus.FAILED
                workflow_job.error_message = f"File injection failed: {str(e)}"
                db.commit()
            
            db.close()
        except Exception:
            pass
        
        # Retry if possible
        if self.request.retries < self.max_retries:
            logger.info(f"Retrying file injection (attempt {self.request.retries + 1})")
            raise self.retry(countdown=60, exc=e)
        
        raise


def _inject_file_into_vm(vm_instance, file_content: bytes, filename: str, injection_config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Inject file content into VM container.
    
    Args:
        vm_instance: VM instance database record
        file_content: File content as bytes
        filename: Original filename
        injection_config: Injection configuration
        
    Returns:
        Dictionary with injection details
    """
    try:
        if not vm_instance.vm_id:
            raise ValueError("No container ID for VM")
        
        # Determine target path
        target_path = injection_config.get("target_path", f"/tmp/{filename}")
        permissions = injection_config.get("permissions", "0755")
        
        # Create temporary file
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            temp_file.write(file_content)
            temp_file_path = temp_file.name
        
        try:
            # Copy file to container
            docker_cp_cmd = [
                "docker", "cp", 
                temp_file_path,
                f"{vm_instance.vm_id}:{target_path}"
            ]
            
            subprocess.run(docker_cp_cmd, capture_output=True, text=True, check=True)
            
            # Set permissions
            chmod_cmd = [
                "docker", "exec", vm_instance.vm_id,
                "chmod", permissions, target_path
            ]
            
            subprocess.run(chmod_cmd, capture_output=True, text=True, check=True)
            
            # Verify injection
            verify_cmd = [
                "docker", "exec", vm_instance.vm_id,
                "ls", "-la", target_path
            ]
            
            verify_result = subprocess.run(verify_cmd, capture_output=True, text=True, check=True)
            
            logger.info(f"File injected successfully: {target_path}")
            logger.debug(f"Verification: {verify_result.stdout}")
            
            return {
                "target_path": target_path,
                "permissions": permissions,
                "injection_time": str(datetime.utcnow()),
                "verification": verify_result.stdout.strip()
            }
            
        finally:
            # Clean up temporary file
            try:
                os.unlink(temp_file_path)
            except Exception:
                pass
        
    except subprocess.CalledProcessError as e:
        logger.error(f"File injection failed: {e}")
        raise RuntimeError(f"Failed to inject file into VM: {e}")


def _start_elk_monitoring(vm_instance, workflow_job, run_number: int = 1):
    """
    Start dual ECS logging for the VM (install-time and runtime).

    Args:
        vm_instance: VM instance database record
        workflow_job: Workflow job database record
        run_number: Run number for this execution

    Returns:
        str: Install UUID for tracking
    """
    try:
        # Configure dual ECS indices
        install_index = f"turdparty-install-ecs-{datetime.now().strftime('%Y.%m.%d')}"
        runtime_index = f"turdparty-runtime-ecs-{datetime.now().strftime('%Y.%m.%d')}"

        logger.info(f"Starting dual ECS logging for VM {vm_instance.name}")
        logger.info(f"Install index: {install_index}")
        logger.info(f"Runtime index: {runtime_index}")

        # Update VM with ELK indices
        vm_instance.elk_index = f"{install_index},{runtime_index}"

        # Update workflow job with ELK configuration
        if not workflow_job.elk_indices:
            workflow_job.elk_indices = []

        workflow_job.elk_indices.append({
            "install_index": install_index,
            "runtime_index": runtime_index,
            "vm_instance_id": str(vm_instance.id),
            "started_at": str(datetime.utcnow()),
            "logging_type": "dual_ecs"
        })

        # Send install-time ECS event with run tracking
        install_uuid = _send_install_ecs_event(vm_instance, workflow_job, run_number)

        logger.info(f"Dual ECS logging configuration completed for run #{run_number}")

        return install_uuid

    except Exception as e:
        logger.error(f"Failed to start ELK monitoring: {e}")
        # Don't fail the injection for monitoring setup issues
        return None


def _send_install_ecs_event(vm_instance, workflow_job, run_number: int = 1):
    """Send install-time ECS event to Elasticsearch with run tracking."""
    try:
        import requests
        import uuid

        # Generate install UUID and run ID
        install_uuid = str(uuid.uuid4())
        file_id = str(workflow_job.file_upload_id) if workflow_job.file_upload_id else "unknown"
        run_id = f"{file_id}-run-{run_number}"

        # Create ECS-compliant install event with run tracking
        ecs_event = {
            "@timestamp": datetime.now().isoformat() + "Z",
            "ecs": {"version": "8.11.0"},
            "event": {
                "kind": "event",
                "category": ["file", "process"],
                "type": ["installation", "change"],
                "action": "file_injection",
                "outcome": "success"
            },
            "turdparty": {
                "workflow_id": str(workflow_job.id),
                "file_id": file_id,
                "vm_id": str(vm_instance.id),
                "phase": "install",
                "install_uuid": install_uuid,
                "run_id": run_id,
                "run_number": run_number,
                "execution_type": "install",
                "footprint_type": "install"
            },
            "file": {
                "name": vm_instance.injection_path.split('/')[-1] if vm_instance.injection_path else "unknown",
                "path": vm_instance.injection_path or "/tmp/unknown",
                "hash": {
                    "sha256": "unknown"  # Would be populated from file_upload record
                }
            },
            "process": {
                "name": "docker_injection",
                "command_line": f"docker cp file {vm_instance.vm_id}:{vm_instance.injection_path}"
            },
            "host": {
                "name": vm_instance.name,
                "id": vm_instance.vm_id or "unknown"
            },
            "message": f"File injected into VM {vm_instance.name} at {vm_instance.injection_path} (Run #{run_number})"
        }

        # Send to Elasticsearch via HTTP API
        es_url = f"http://elasticsearch:9200/turdparty-install-ecs-{datetime.now().strftime('%Y.%m.%d')}/_doc"

        response = requests.post(
            es_url,
            json=ecs_event,
            headers={"Content-Type": "application/json"},
            timeout=10
        )

        if response.status_code in [200, 201]:
            logger.info(f"Install ECS event sent successfully: {install_uuid} (Run #{run_number})")

            # Update workflow job with install UUID and run info
            if not workflow_job.results:
                workflow_job.results = {}
            workflow_job.results.update({
                "install_uuid": install_uuid,
                "run_number": run_number,
                "run_id": run_id
            })

            return install_uuid

        else:
            logger.error(f"Failed to send install ECS event: {response.status_code}")
            return None

    except Exception as e:
        logger.error(f"Failed to send install ECS event: {e}")
        return None


@shared_task(bind=True, max_retries=2, retry_backoff=True)
def execute_injected_file(self, workflow_job_id: str, execution_config: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    Execute the injected file in the VM.
    
    Args:
        workflow_job_id: ID of the workflow job
        execution_config: Execution configuration
        
    Returns:
        Dictionary with execution details
    """
    try:
        # Import here to avoid circular imports
        from services.api.src.services.database import SessionLocal
        from services.api.src.models.workflow_job import WorkflowJob
        from services.api.src.models.vm_instance import VMInstance
        from datetime import datetime
        
        db = SessionLocal()
        
        try:
            # Get workflow job and VM
            workflow_job = db.query(WorkflowJob).filter(
                WorkflowJob.id == UUID(workflow_job_id)
            ).first()
            
            if not workflow_job or not workflow_job.vm_instance_id:
                raise ValueError(f"Workflow job or VM not found: {workflow_job_id}")
            
            vm_instance = db.query(VMInstance).filter(
                VMInstance.id == workflow_job.vm_instance_id
            ).first()
            
            if not vm_instance or not vm_instance.injection_completed:
                raise ValueError("VM or file injection not ready")
            
            logger.info(f"Executing injected file in VM {vm_instance.name}")
            
            # Execute the injected file
            execution_result = _execute_file_in_vm(vm_instance, execution_config or {})
            
            # Update workflow results
            if not workflow_job.results:
                workflow_job.results = {}
            
            workflow_job.results.update({
                "execution_completed": True,
                "execution_time": str(datetime.utcnow()),
                "execution_result": execution_result
            })
            
            db.commit()
            
            logger.info(f"File execution completed in VM {vm_instance.name}")
            
            return {
                "status": "success",
                "vm_instance_id": str(vm_instance.id),
                "execution_result": execution_result,
                "workflow_job_id": workflow_job_id
            }
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"File execution failed: {e}")
        raise


def _execute_file_in_vm(vm_instance, execution_config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Execute file in VM container.
    
    Args:
        vm_instance: VM instance database record
        execution_config: Execution configuration
        
    Returns:
        Dictionary with execution results
    """
    try:
        if not vm_instance.vm_id or not vm_instance.injection_path:
            raise ValueError("VM or injection path not available")
        
        # Execute the injected file
        exec_cmd = [
            "docker", "exec", vm_instance.vm_id,
            "bash", "-c", f"cd /tmp && {vm_instance.injection_path}"
        ]
        
        result = subprocess.run(
            exec_cmd, 
            capture_output=True, 
            text=True, 
            timeout=300  # 5 minute timeout
        )
        
        logger.info(f"File execution completed with return code: {result.returncode}")
        
        return {
            "return_code": result.returncode,
            "stdout": result.stdout,
            "stderr": result.stderr,
            "execution_time": str(datetime.utcnow())
        }
        
    except subprocess.TimeoutExpired:
        logger.warning("File execution timed out")
        return {
            "return_code": -1,
            "stdout": "",
            "stderr": "Execution timed out",
            "execution_time": str(datetime.utcnow())
        }
    except subprocess.CalledProcessError as e:
        logger.error(f"File execution failed: {e}")
        return {
            "return_code": e.returncode,
            "stdout": e.stdout or "",
            "stderr": e.stderr or "",
            "execution_time": str(datetime.utcnow())
        }
