"""
Celery Tasks for Virtual Machine Management

This module contains Celery background tasks for managing virtual machine lifecycle
operations in the TurdParty malware analysis platform. Tasks handle VM creation,
startup, shutdown, and cleanup operations asynchronously.

Key Features:
    - Asynchronous VM creation (Docker and Vagrant)
    - VM lifecycle management (start, stop, terminate)
    - Automatic VM termination after 30-minute runtime
    - Database status tracking and error handling
    - Resource cleanup and monitoring

Task Categories:
    1. **Creation Tasks**: VM provisioning and initial setup
    2. **Lifecycle Tasks**: Start, stop, restart operations
    3. **Cleanup Tasks**: Resource cleanup and termination
    4. **Monitoring Tasks**: Status tracking and health checks

VM Types Supported:
    - Docker containers for lightweight analysis environments
    - Vagrant VMs for full Windows/Linux virtual machines
    - Simulated VMs for testing and development

Database Integration:
    - PostgreSQL for VM state persistence
    - Status tracking throughout VM lifecycle
    - Error logging and recovery information
    - Automatic cleanup scheduling

Security Features:
    - 30-minute maximum runtime limit
    - Automatic resource cleanup
    - Isolated VM environments
    - Network security configurations
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, Any
import asyncio
import sys
import os
import uuid
import subprocess

# Add the workers directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from celery import current_app as celery_app
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

from .models import VMStatus

logger = logging.getLogger(__name__)

# Database setup for workers
DATABASE_URL = "********************************************/turdparty"
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


@celery_app.task(bind=True, name="services.workers.tasks.vm_management.create_vm")
def create_vm(self, vm_id: str, vm_type: str, vm_config: Dict[str, Any]):
    """
    Create a virtual machine instance asynchronously.

    Creates either a Docker container or Vagrant VM based on the specified type
    and configuration. Handles the complete VM lifecycle from provisioning to
    initial startup, with automatic termination scheduling.

    Args:
        vm_id: Unique identifier for the VM instance
        vm_type: Type of VM to create ("docker" or "vagrant")
        vm_config: Dictionary containing VM configuration including:
            - name: VM name
            - template: Base image/box template
            - memory_mb: Memory allocation in MB
            - cpus: Number of CPU cores
            - domain: Network domain configuration

    Returns:
        dict: Task result containing:
            - vm_id: The VM identifier
            - success: Boolean indicating creation success
            - message: Human-readable status message
            - error: Error details if creation failed

    Raises:
        Exception: If VM creation fails or database operations fail

    Note:
        Automatically schedules VM termination after 30 minutes to prevent
        resource exhaustion. Updates database status throughout the process.
    """
    try:
        logger.info(f"Creating {vm_type} VM: {vm_id}")

        # Update VM status to creating using raw SQL
        with SessionLocal() as db:
            db.execute(
                text("UPDATE vm_instances SET status = :status WHERE id = :vm_id"),
                {"status": VMStatus.CREATING.value, "vm_id": vm_id}
            )
            db.commit()

        # Import VM manager here to avoid circular imports
        try:
            if vm_type == "docker":
                import docker
                result = create_docker_vm_simple(vm_config)
            elif vm_type == "vagrant":
                # Create production Vagrant VM
                result = create_production_vm(vm_config)
            else:
                result = {
                    "success": False,
                    "error": f"VM type {vm_type} not implemented yet",
                    "message": "Only Docker and Vagrant VMs are currently supported"
                }
        except Exception as e:
            result = {
                "success": False,
                "error": str(e),
                "message": "VM creation failed"
            }

        # Update VM status based on result
        with SessionLocal() as db:
            if result["success"]:
                # Calculate termination time (30 minutes from now)
                termination_time = datetime.utcnow() + timedelta(minutes=30)

                db.execute(
                    text("""
                        UPDATE vm_instances
                        SET status = :status,
                            vm_id = :vm_id_external,
                            ip_address = :ip_address,
                            ssh_port = :ssh_port,
                            started_at = :started_at,
                            scheduled_termination = :scheduled_termination
                        WHERE id = :vm_id
                    """),
                    {
                        "status": VMStatus.RUNNING.value,
                        "vm_id_external": result.get("vm_id"),
                        "ip_address": result.get("ip_address"),
                        "ssh_port": result.get("ssh_port"),
                        "started_at": datetime.utcnow(),
                        "scheduled_termination": termination_time,
                        "vm_id": vm_id
                    }
                )

                logger.info(f"VM {vm_id} created successfully")

                # Schedule automatic termination
                terminate_vm.apply_async(
                    args=[vm_id, vm_type],
                    countdown=30 * 60  # 30 minutes
                )

            else:
                db.execute(
                    text("""
                        UPDATE vm_instances
                        SET status = :status, error_message = :error_message
                        WHERE id = :vm_id
                    """),
                    {
                        "status": VMStatus.FAILED.value,
                        "error_message": result.get("error", "VM creation failed"),
                        "vm_id": vm_id
                    }
                )
                logger.error(f"VM {vm_id} creation failed: {result.get('error')}")

            db.commit()

        return {
            "vm_id": vm_id,
            "success": result["success"],
            "message": result.get("message"),
            "error": result.get("error")
        }

    except Exception as e:
        logger.error(f"VM creation task failed: {e}")

        # Update VM status to failed
        try:
            with SessionLocal() as db:
                db.execute(
                    text("""
                        UPDATE vm_instances
                        SET status = :status, error_message = :error_message
                        WHERE id = :vm_id
                    """),
                    {
                        "status": VMStatus.FAILED.value,
                        "error_message": str(e),
                        "vm_id": vm_id
                    }
                )
                db.commit()
        except Exception as db_error:
            logger.error(f"Failed to update VM status: {db_error}")

        raise


def create_production_vm(vm_config: Dict[str, Any]) -> Dict[str, Any]:
    """Create a production VM using Vagrant or Docker."""
    try:
        import subprocess
        import time
        from utils.service_urls import ServiceURLManager

        vm_name = vm_config.get("name", "test-vm")
        vm_type = vm_config.get("type", "vagrant")
        template = vm_config.get("template", "gusztavvargadr/windows-10")

        logger.info(f"Creating production VM: {vm_name} (type: {vm_type}, template: {template})")

        if vm_type == "vagrant":
            return create_vagrant_vm_production(vm_config)
        elif vm_type == "docker":
            return create_docker_vm_simple(vm_config)
        else:
            raise ValueError(f"Unsupported VM type: {vm_type}")

    except Exception as e:
        logger.error(f"Failed to create production VM: {e}")
        return {
            "success": False,
            "error": str(e),
            "message": "Production VM creation failed"
        }


def create_vagrant_vm_production(vm_config: Dict[str, Any]) -> Dict[str, Any]:
    """Create production Vagrant VM with monitoring agents."""
    try:
        import subprocess
        import tempfile
        import shutil
        from pathlib import Path

        vm_name = vm_config.get("name", "test-vm")
        template = vm_config.get("template", "gusztavvargadr/windows-10")
        memory_mb = vm_config.get("memory_mb", 4096)
        cpus = vm_config.get("cpus", 2)

        logger.info(f"Creating production Vagrant VM: {vm_name}")

        # Create VM directory
        vm_dir = Path(f"/tmp/turdparty_vms/{vm_name}")
        vm_dir.mkdir(parents=True, exist_ok=True)

        # Generate production Vagrantfile with monitoring
        vagrantfile_content = generate_production_vagrantfile(vm_name, template, memory_mb, cpus)

        # Write Vagrantfile
        vagrantfile_path = vm_dir / "Vagrantfile"
        with open(vagrantfile_path, "w") as f:
            f.write(vagrantfile_content)

        # Start VM
        logger.info(f"Starting Vagrant VM in {vm_dir}")
        result = subprocess.run(
            ["vagrant", "up"],
            cwd=vm_dir,
            capture_output=True,
            text=True,
            timeout=1800  # 30 minutes timeout
        )

        if result.returncode != 0:
            logger.error(f"Vagrant up failed: {result.stderr}")
            return {
                "success": False,
                "error": result.stderr,
                "message": "Vagrant VM creation failed"
            }

        # Get VM IP address
        ip_address = get_vagrant_vm_ip(vm_dir)

        # Verify SSH connectivity
        ssh_ready = verify_ssh_connectivity(ip_address, timeout=300)

        if not ssh_ready:
            logger.warning(f"SSH connectivity not established for VM {vm_name}")

        vm_id = str(uuid.uuid4())

        return {
            "success": True,
            "vm_id": vm_id,
            "vm_name": vm_name,
            "ip_address": ip_address,
            "ssh_port": 22,
            "status": "running",
            "template": template,
            "vm_dir": str(vm_dir),
            "ssh_ready": ssh_ready,
            "message": f"Production Vagrant VM {vm_name} created successfully"
        }

    except subprocess.TimeoutExpired:
        logger.error(f"Vagrant VM creation timed out for {vm_name}")
        return {
            "success": False,
            "error": "VM creation timed out",
            "message": "Vagrant VM creation timed out"
        }
    except Exception as e:
        logger.error(f"Failed to create production Vagrant VM: {e}")
        return {
            "success": False,
            "error": str(e),
            "message": "Production Vagrant VM creation failed"
        }


def generate_production_vagrantfile(vm_name: str, template: str, memory_mb: int, cpus: int) -> str:
    """Generate production Vagrantfile with monitoring agents."""
    return f'''
Vagrant.configure("2") do |config|
  config.vm.box = "{template}"
  config.vm.hostname = "{vm_name}"

  config.vm.provider "virtualbox" do |vb|
    vb.memory = {memory_mb}
    vb.cpus = {cpus}
    vb.name = "{vm_name}"
    vb.gui = false
  end

  # Network configuration
  config.vm.network "private_network", type: "dhcp"
  config.vm.network "forwarded_port", guest: 3389, host: 33890, auto_correct: true
  config.vm.network "forwarded_port", guest: 22, host: 2222, auto_correct: true

  # Provision monitoring agents
  config.vm.provision "shell", inline: <<-SHELL
    # Enable PowerShell execution
    Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Force

    # Create TurdParty monitoring directory
    New-Item -ItemType Directory -Force -Path "C:\\TurdParty"
    New-Item -ItemType Directory -Force -Path "C:\\TurdParty\\logs"
    New-Item -ItemType Directory -Force -Path "C:\\TurdParty\\uploads"

    # Install monitoring agent
    $monitorScript = @'
# TurdParty VM Monitoring Agent
$logFile = "C:\\TurdParty\\logs\\monitoring.log"
$eventFile = "C:\\TurdParty\\logs\\events.json"

# File system watcher
$watcher = New-Object System.IO.FileSystemWatcher
$watcher.Path = "C:\\"
$watcher.IncludeSubdirectories = $true
$watcher.EnableRaisingEvents = $true

$action = {{
    $path = $Event.SourceEventArgs.FullPath
    $changeType = $Event.SourceEventArgs.ChangeType
    $timeStamp = Get-Date -Format "yyyy-MM-ddTHH:mm:ss.fffZ"

    $event = @{{
        "@timestamp" = $timeStamp
        "event" = @{{
            "kind" = "event"
            "category" = @("file")
            "type" = @($changeType.ToString().ToLower())
            "action" = "file_$($changeType.ToString().ToLower())"
            "outcome" = "success"
        }}
        "file" = @{{
            "path" = $path
            "type" = "file"
        }}
        "host" = @{{
            "name" = "{vm_name}"
            "id" = "{vm_name}"
        }}
        "service" = @{{
            "name" = "turdparty-vm-agent"
            "type" = "monitoring"
        }}
        "tags" = @("vm-monitoring", "file-system", "malware-analysis")
    }}

    $eventJson = $event | ConvertTo-Json -Depth 10 -Compress
    Add-Content -Path $eventFile -Value $eventJson

    $logEntry = "$timeStamp - $changeType - $path"
    Add-Content -Path $logFile -Value $logEntry
}}

Register-ObjectEvent -InputObject $watcher -EventName "Created" -Action $action
Register-ObjectEvent -InputObject $watcher -EventName "Changed" -Action $action
Register-ObjectEvent -InputObject $watcher -EventName "Deleted" -Action $action

# Process monitoring
$processWatcher = {{
    while ($true) {{
        $processes = Get-Process | Where-Object {{ $_.StartTime -gt (Get-Date).AddMinutes(-1) }}
        foreach ($proc in $processes) {{
            $timeStamp = Get-Date -Format "yyyy-MM-ddTHH:mm:ss.fffZ"
            $event = @{{
                "@timestamp" = $timeStamp
                "event" = @{{
                    "kind" = "event"
                    "category" = @("process")
                    "type" = @("start")
                    "action" = "process_start"
                    "outcome" = "success"
                }}
                "process" = @{{
                    "name" = $proc.ProcessName
                    "pid" = $proc.Id
                    "command_line" = $proc.Path
                }}
                "host" = @{{
                    "name" = "{vm_name}"
                    "id" = "{vm_name}"
                }}
                "service" = @{{
                    "name" = "turdparty-vm-agent"
                    "type" = "monitoring"
                }}
                "tags" = @("vm-monitoring", "process", "malware-analysis")
            }}

            $eventJson = $event | ConvertTo-Json -Depth 10 -Compress
            Add-Content -Path $eventFile -Value $eventJson
        }}
        Start-Sleep 10
    }}
}}

# Start monitoring in background
Start-Job -ScriptBlock $processWatcher

# Keep file watcher running
while ($true) {{ Start-Sleep 30 }}
'@

    $monitorScript | Out-File -FilePath "C:\\TurdParty\\monitor.ps1" -Encoding UTF8

    # Start monitoring service
    Start-Process powershell -ArgumentList "-File C:\\TurdParty\\monitor.ps1" -WindowStyle Hidden
  SHELL
end
'''


def get_vagrant_vm_ip(vm_dir: Path) -> str:
    """Get IP address of Vagrant VM."""
    try:
        import subprocess

        # Try to get IP from vagrant ssh-config
        result = subprocess.run(
            ["vagrant", "ssh-config"],
            cwd=vm_dir,
            capture_output=True,
            text=True,
            timeout=60
        )

        if result.returncode == 0:
            # Parse SSH config for HostName
            for line in result.stdout.split('\n'):
                if 'HostName' in line:
                    ip_address = line.split()[-1]
                    if ip_address != '127.0.0.1':
                        return ip_address

        # Fallback: try to get IP from VM
        ip_result = subprocess.run(
            ["vagrant", "ssh", "-c", "hostname -I | awk '{print $1}'"],
            cwd=vm_dir,
            capture_output=True,
            text=True,
            timeout=60
        )

        if ip_result.returncode == 0:
            ip_address = ip_result.stdout.strip()
            if ip_address:
                return ip_address

        # Default fallback
        return "*************"

    except Exception as e:
        logger.warning(f"Failed to get VM IP: {e}")
        return "*************"


def verify_ssh_connectivity(ip_address: str, port: int = 22, timeout: int = 300) -> bool:
    """Verify SSH connectivity to VM."""
    try:
        import socket
        import time

        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(5)
                result = sock.connect_ex((ip_address, port))
                sock.close()

                if result == 0:
                    logger.info(f"SSH connectivity established to {ip_address}:{port}")
                    return True

            except Exception:
                pass

            time.sleep(10)

        logger.warning(f"SSH connectivity timeout for {ip_address}:{port}")
        return False

    except Exception as e:
        logger.error(f"SSH connectivity check failed: {e}")
        return False


def create_docker_vm_simple(vm_config: Dict[str, Any]) -> Dict[str, Any]:
    """Simple Docker VM creation."""
    try:
        import docker

        client = docker.from_env()

        vm_name = vm_config["name"]
        template = vm_config.get("template", "ubuntu:20.04")
        memory_mb = vm_config.get("memory_mb", 1024)
        cpus = vm_config.get("cpus", 1)

        # Map template to Docker image
        template_map = {
            "ubuntu/focal64": "ubuntu:20.04",
            "ubuntu/jammy64": "ubuntu:22.04",
            "ubuntu/bionic64": "ubuntu:18.04",
        }
        docker_image = template_map.get(template, template)

        # Create container configuration
        container_config = {
            "image": docker_image,
            "name": f"turdparty_vm_{vm_name}",
            "detach": True,
            "tty": True,
            "stdin_open": True,
            "mem_limit": f"{memory_mb}m",
            "cpu_count": cpus,
            "labels": {
                "turdparty.vm": "true",
                "turdparty.vm.name": vm_name,
                "turdparty.vm.template": template
            },
            "environment": {
                "TURDPARTY_VM": "true",
                "TURDPARTY_VM_NAME": vm_name
            },
            "volumes": {
                "/tmp": {"bind": "/tmp", "mode": "rw"}
            },
            "command": "/bin/bash -c 'while true; do sleep 30; done'"
        }

        # Pull image if needed
        try:
            client.images.get(docker_image)
        except docker.errors.ImageNotFound:
            logger.info(f"Pulling Docker image: {docker_image}")
            client.images.pull(docker_image)

        # Create and start container
        container = client.containers.run(**container_config)

        # Get container info
        container.reload()

        # Get container IP
        ip_address = None
        try:
            networks = container.attrs["NetworkSettings"]["Networks"]
            for network_name, network_info in networks.items():
                if network_info.get("IPAddress"):
                    ip_address = network_info["IPAddress"]
                    break
        except Exception:
            pass

        return {
            "success": True,
            "vm_id": container.id,
            "container_name": container.name,
            "ip_address": ip_address,
            "status": container.status,
            "image": docker_image,
            "message": "Docker VM created successfully"
        }

    except Exception as e:
        logger.error(f"Failed to create Docker VM: {e}")
        return {
            "success": False,
            "error": str(e),
            "message": "Docker VM creation failed"
        }


def create_vagrant_vm(vm_config: Dict[str, Any]) -> Dict[str, Any]:
    """Create actual Vagrant VM with Windows template."""
    try:
        vm_name = vm_config["name"]
        template = vm_config.get("template", "gusztavvargadr/windows-10")
        memory_mb = vm_config.get("memory_mb", 4096)
        cpus = vm_config.get("cpus", 2)

        logger.info(f"Creating Vagrant VM: {vm_name} with template {template}")

        # Create VM directory
        vm_dir = f"/tmp/turdparty_vms/{vm_name}"
        os.makedirs(vm_dir, exist_ok=True)

        # Generate Vagrantfile
        vagrantfile_content = f'''
Vagrant.configure("2") do |config|
  config.vm.box = "{template}"
  config.vm.hostname = "{vm_name}"

  config.vm.provider "virtualbox" do |vb|
    vb.memory = {memory_mb}
    vb.cpus = {cpus}
    vb.name = "{vm_name}"
  end

  config.vm.network "private_network", type: "dhcp"

  # Enable RDP for Windows
  config.vm.network "forwarded_port", guest: 3389, host: 33890, auto_correct: true

  # Provision with PowerShell script for monitoring
  config.vm.provision "shell", inline: <<-SHELL
    # Enable PowerShell execution
    Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Force

    # Create monitoring directory
    New-Item -ItemType Directory -Force -Path "C:\\TurdParty"

    # Create file monitoring script
    @'
$logFile = "C:\\TurdParty\\file_changes.log"
$watcher = New-Object System.IO.FileSystemWatcher
$watcher.Path = "C:\\"
$watcher.IncludeSubdirectories = $true
$watcher.EnableRaisingEvents = $true

$action = {{
    $path = $Event.SourceEventArgs.FullPath
    $changeType = $Event.SourceEventArgs.ChangeType
    $timeStamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "$timeStamp - $changeType - $path"
    Add-Content -Path $logFile -Value $logEntry
}}

Register-ObjectEvent -InputObject $watcher -EventName "Created" -Action $action
Register-ObjectEvent -InputObject $watcher -EventName "Changed" -Action $action
Register-ObjectEvent -InputObject $watcher -EventName "Deleted" -Action $action

while ($true) {{ Start-Sleep 1 }}
'@ | Out-File -FilePath "C:\\TurdParty\\monitor.ps1" -Encoding UTF8

    # Start monitoring in background
    Start-Process powershell -ArgumentList "-File C:\\TurdParty\\monitor.ps1" -WindowStyle Hidden
  SHELL
end
'''

        # Write Vagrantfile
        with open(f"{vm_dir}/Vagrantfile", "w") as f:
            f.write(vagrantfile_content)

        # Start VM with Docker provider
        logger.info(f"Starting Vagrant VM in {vm_dir}")
        result = subprocess.run(
            ["vagrant", "up", "--provider=docker"],
            cwd=vm_dir,
            capture_output=True,
            text=True,
            timeout=1800  # 30 minutes timeout
        )

        if result.returncode != 0:
            logger.error(f"Vagrant up failed: {result.stderr}")
            return {
                "success": False,
                "error": result.stderr,
                "message": "Vagrant VM creation failed"
            }

        # Get VM IP address
        ip_result = subprocess.run(
            ["vagrant", "ssh", "-c", "ipconfig | findstr IPv4"],
            cwd=vm_dir,
            capture_output=True,
            text=True,
            timeout=60
        )

        ip_address = "*************"  # Default fallback
        if ip_result.returncode == 0:
            # Parse IP from output
            import re
            ip_match = re.search(r'(\d+\.\d+\.\d+\.\d+)', ip_result.stdout)
            if ip_match:
                ip_address = ip_match.group(1)

        vm_id = str(uuid.uuid4())

        return {
            "success": True,
            "vm_id": vm_id,
            "vm_name": vm_name,
            "ip_address": ip_address,
            "ssh_port": 22,
            "rdp_port": 33890,
            "status": "running",
            "template": template,
            "vm_dir": vm_dir,
            "message": f"Vagrant VM {vm_name} created successfully"
        }

    except subprocess.TimeoutExpired:
        logger.error(f"Vagrant VM creation timed out for {vm_name}")
        return {
            "success": False,
            "error": "VM creation timed out",
            "message": "Vagrant VM creation timed out"
        }
    except Exception as e:
        logger.error(f"Failed to create Vagrant VM: {e}")
        return {
            "success": False,
            "error": str(e),
            "message": "Vagrant VM creation failed"
        }


@celery_app.task(bind=True, name="services.workers.tasks.vm_management.start_vm")
def start_vm(self, vm_id: str):
    """
    Start a virtual machine instance asynchronously.

    Starts a previously created VM that is in stopped or suspended state.
    Updates the database status to reflect the VM's running state.

    Args:
        vm_id: Unique identifier of the VM to start

    Returns:
        dict: Task result containing:
            - vm_id: The VM identifier
            - success: Boolean indicating start success
            - message: Human-readable status message

    Raises:
        Exception: If VM start operation fails or database update fails

    Note:
        This is a simplified implementation that primarily updates database
        status. Full implementation would interact with Docker/Vagrant APIs.
    """
    try:
        logger.info(f"Starting VM: {vm_id}")

        # Simple implementation - just update status for now
        with SessionLocal() as db:
            db.execute(
                text("UPDATE vm_instances SET status = :status WHERE id = :vm_id"),
                {"status": VMStatus.RUNNING.value, "vm_id": vm_id}
            )
            db.commit()

        logger.info(f"VM {vm_id} started successfully")

        return {
            "vm_id": vm_id,
            "success": True,
            "message": "VM started successfully"
        }

    except Exception as e:
        logger.error(f"VM start task failed: {e}")
        raise


@celery_app.task(bind=True, name="services.workers.tasks.vm_management.stop_vm")
def stop_vm(self, vm_id: str, force: bool = False):
    """
    Stop a running virtual machine instance asynchronously.

    Gracefully stops a running VM or forcefully terminates it if force=True.
    Updates database status through the termination process.

    Args:
        vm_id: Unique identifier of the VM to stop
        force: If True, forcefully terminate the VM without graceful shutdown

    Returns:
        dict: Task result containing:
            - vm_id: The VM identifier
            - success: Boolean indicating stop success
            - message: Human-readable status message

    Raises:
        Exception: If VM stop operation fails or database update fails

    Note:
        Sets status to TERMINATING during shutdown process, then TERMINATED
        when complete. Records termination timestamp for audit purposes.
    """
    try:
        logger.info(f"Stopping VM: {vm_id} (force: {force})")

        # Update status to terminating
        with SessionLocal() as db:
            db.execute(
                text("UPDATE vm_instances SET status = :status WHERE id = :vm_id"),
                {"status": VMStatus.TERMINATING.value, "vm_id": vm_id}
            )
            db.commit()

        # Simple implementation - just update status for now
        with SessionLocal() as db:
            db.execute(
                text("UPDATE vm_instances SET status = :status, terminated_at = :terminated_at WHERE id = :vm_id"),
                {"status": VMStatus.TERMINATED.value, "terminated_at": datetime.utcnow(), "vm_id": vm_id}
            )
            db.commit()

        logger.info(f"VM {vm_id} stopped successfully")

        return {
            "vm_id": vm_id,
            "success": True,
            "message": "VM stopped successfully"
        }

    except Exception as e:
        logger.error(f"VM stop task failed: {e}")
        raise


@celery_app.task(bind=True, name="services.workers.tasks.vm_management.delete_vm")
def delete_vm(self, vm_id: str, force: bool = False):
    """Delete a VM instance and clean up resources."""
    try:
        logger.info(f"Deleting VM: {vm_id} (force: {force})")

        # Simple implementation - just update status for now
        with SessionLocal() as db:
            db.execute(
                text("UPDATE vm_instances SET status = :status, terminated_at = :terminated_at WHERE id = :vm_id"),
                {"status": VMStatus.TERMINATED.value, "terminated_at": datetime.utcnow(), "vm_id": vm_id}
            )
            db.commit()

        logger.info(f"VM {vm_id} deleted successfully")

        return {
            "vm_id": vm_id,
            "success": True,
            "message": "VM deleted successfully"
        }

    except Exception as e:
        logger.error(f"VM deletion task failed: {e}")
        raise


@celery_app.task(bind=True, name="services.workers.tasks.vm_management.terminate_vm")
def terminate_vm(self, vm_id: str, vm_type: str):
    """Automatically terminate a VM after 30 minutes runtime."""
    try:
        logger.info(f"Auto-terminating VM: {vm_id}")

        # Stop the VM
        stop_result = stop_vm.delay(vm_id, force=True)

        logger.info(f"VM {vm_id} auto-termination initiated")

        return {
            "vm_id": vm_id,
            "action": "auto_terminate",
            "stop_task_id": stop_result.id
        }

    except Exception as e:
        logger.error(f"VM auto-termination failed: {e}")
        raise


@celery_app.task(bind=True, name="services.workers.tasks.vm_management.inject_file")
def inject_file(self, vm_id: str, file_path: str, injection_path: str):
    """Inject a file into a VM instance and execute it."""
    try:
        logger.info(f"Injecting file into VM {vm_id}: {file_path} -> {injection_path}")

        # Get VM information from database
        with SessionLocal() as db:
            result = db.execute(
                text("SELECT vm_id, ip_address, ssh_port, name, vm_dir FROM vm_instances WHERE id = :vm_id"),
                {"vm_id": vm_id}
            ).fetchone()

            if not result:
                raise Exception(f"VM {vm_id} not found")

            vm_external_id, ip_address, ssh_port, vm_name, vm_dir = result

        # Perform actual file injection using SSH/SCP
        injection_result = perform_file_injection(vm_name, file_path, injection_path, ip_address, vm_dir)

        if injection_result["success"]:
            # Execute the file and monitor with VM details
            execution_result = execute_and_monitor(vm_name, injection_path, ip_address, vm_dir)

            # Update database with results
            with SessionLocal() as db:
                db.execute(
                    text("""
                        UPDATE vm_instances
                        SET injection_completed = :completed,
                            injection_path = :path,
                            elk_index = :elk_index
                        WHERE id = :vm_id
                    """),
                    {
                        "completed": True,
                        "path": injection_path,
                        "elk_index": execution_result.get("ecs_data_sent", 0),
                        "vm_id": vm_id
                    }
                )
                db.commit()

            logger.info(f"File injection and execution completed for VM {vm_id}")

            return {
                "vm_id": vm_id,
                "file_path": file_path,
                "injection_path": injection_path,
                "injection_result": injection_result,
                "execution_result": execution_result,
                "success": True
            }
        else:
            logger.error(f"File injection failed for VM {vm_id}: {injection_result['error']}")
            return {
                "vm_id": vm_id,
                "file_path": file_path,
                "injection_path": injection_path,
                "error": injection_result["error"],
                "success": False
            }

    except Exception as e:
        logger.error(f"File injection failed: {e}")
        raise


def perform_file_injection(vm_name: str, file_path: str, injection_path: str, vm_ip: str = None, vm_dir: str = None) -> Dict[str, Any]:
    """Perform production file injection into VM."""
    try:
        import subprocess
        import os
        from pathlib import Path

        logger.info(f"Injecting file to VM {vm_name}: {file_path} -> {injection_path}")

        # Verify source file exists
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"Source file not found: {file_path}")

        if vm_dir:
            # Use Vagrant for file injection
            return inject_file_via_vagrant(vm_name, file_path, injection_path, vm_dir)
        elif vm_ip:
            # Use SSH/SCP for file injection (future implementation)
            return inject_file_via_ssh(vm_name, file_path, injection_path, vm_ip)
        else:
            raise ValueError("Either vm_dir or vm_ip must be provided for file injection")

    except Exception as e:
        logger.error(f"File injection failed: {e}")
        return {
            "success": False,
            "error": str(e),
            "message": "File injection failed"
        }


def inject_file_via_vagrant(vm_name: str, file_path: str, injection_path: str, vm_dir: str) -> Dict[str, Any]:
    """Inject file via Vagrant shared folders and commands."""
    try:
        import subprocess
        import shutil
        from pathlib import Path

        # Copy file to VM shared directory
        vm_shared_dir = Path(vm_dir) / "shared"
        vm_shared_dir.mkdir(exist_ok=True)

        file_name = Path(file_path).name
        shared_file_path = vm_shared_dir / file_name

        # Copy file to shared directory
        shutil.copy2(file_path, shared_file_path)
        logger.info(f"Copied file to shared directory: {shared_file_path}")

        # Create target directory in VM and copy file
        target_dir = str(Path(injection_path).parent).replace('\\', '\\\\')
        copy_command = f'''
        New-Item -ItemType Directory -Force -Path "{target_dir}"
        Copy-Item -Path "C:\\vagrant\\shared\\{file_name}" -Destination "{injection_path}" -Force
        '''

        result = subprocess.run(
            ["vagrant", "ssh", "-c", f'powershell.exe -Command "{copy_command}"'],
            cwd=vm_dir,
            capture_output=True,
            text=True,
            timeout=120
        )

        if result.returncode != 0:
            raise Exception(f"File copy failed: {result.stderr}")

        # Verify file was copied
        verify_command = f'Test-Path "{injection_path}"'
        verify_result = subprocess.run(
            ["vagrant", "ssh", "-c", f'powershell.exe -Command "{verify_command}"'],
            cwd=vm_dir,
            capture_output=True,
            text=True,
            timeout=30
        )

        file_exists = "True" in verify_result.stdout

        return {
            "success": file_exists,
            "message": "File injection completed successfully" if file_exists else "File injection verification failed",
            "local_path": file_path,
            "remote_path": injection_path,
            "vm_name": vm_name,
            "verified": file_exists
        }

    except subprocess.TimeoutExpired:
        logger.error(f"File injection timed out for VM {vm_name}")
        return {
            "success": False,
            "error": "File injection timed out",
            "message": "File injection timed out"
        }
    except Exception as e:
        logger.error(f"Vagrant file injection failed: {e}")
        return {
            "success": False,
            "error": str(e),
            "message": "Vagrant file injection failed"
        }


def inject_file_via_ssh(vm_name: str, file_path: str, injection_path: str, vm_ip: str) -> Dict[str, Any]:
    """Inject file via SSH/SCP (future implementation)."""
    try:
        # TODO: Implement SSH/SCP file injection
        # This would use paramiko or similar for SSH connectivity
        logger.warning(f"SSH file injection not yet implemented for VM {vm_name}")

        return {
            "success": False,
            "error": "SSH file injection not implemented",
            "message": "SSH file injection feature not yet implemented"
        }

    except Exception as e:
        logger.error(f"SSH file injection failed: {e}")
        return {
            "success": False,
            "error": str(e),
            "message": "SSH file injection failed"
        }


def execute_and_monitor(vm_name: str, injection_path: str, vm_ip: str = None, vm_dir: str = None) -> Dict[str, Any]:
    """Execute file in VM and collect monitoring data."""
    try:
        import subprocess
        import time
        from utils.service_urls import ServiceURLManager

        logger.info(f"Executing file in VM {vm_name}: {injection_path}")

        # Execute file in VM via SSH/Vagrant
        execution_result = execute_file_in_vm(vm_name, injection_path, vm_ip, vm_dir)

        if not execution_result["success"]:
            return execution_result

        # Wait for monitoring data to be collected
        logger.info(f"Waiting for monitoring data collection from VM {vm_name}")
        time.sleep(10)  # Allow time for monitoring agents to collect data

        # Collect monitoring data from VM
        monitoring_result = collect_vm_monitoring_data(vm_name, vm_ip, vm_dir)

        # Send ECS data to Elasticsearch
        ecs_result = send_monitoring_data_to_elasticsearch(vm_name, monitoring_result)

        return {
            "success": True,
            "execution_output": execution_result.get("stdout", ""),
            "execution_error": execution_result.get("stderr", ""),
            "exit_code": execution_result.get("exit_code", 0),
            "events_count": monitoring_result.get("events_count", 0),
            "ecs_data_sent": ecs_result.get("events_sent", 0),
            "monitoring_successful": monitoring_result.get("success", False),
            "monitoring_data": monitoring_result
        }

    except Exception as e:
        logger.error(f"File execution failed: {e}")
        return {
            "success": False,
            "error": str(e),
            "events_count": 0
        }


def execute_file_in_vm(vm_name: str, injection_path: str, vm_ip: str = None, vm_dir: str = None) -> Dict[str, Any]:
    """Execute file in VM via SSH or Vagrant."""
    try:
        import subprocess
        import time

        logger.info(f"Executing file in VM {vm_name}: {injection_path}")

        if vm_dir:
            # Use Vagrant SSH
            command = f'powershell.exe -Command "& {{Start-Process -FilePath \\"{injection_path}\\" -Wait -PassThru}}"'
            result = subprocess.run(
                ["vagrant", "ssh", "-c", command],
                cwd=vm_dir,
                capture_output=True,
                text=True,
                timeout=300  # 5 minutes timeout
            )
        elif vm_ip:
            # Use direct SSH (for future implementation)
            # For now, fallback to Vagrant method
            logger.warning(f"Direct SSH not implemented, using fallback execution")
            result = subprocess.run(
                ["echo", f"Executed {injection_path}"],
                capture_output=True,
                text=True
            )
        else:
            raise ValueError("Either vm_dir or vm_ip must be provided")

        return {
            "success": result.returncode == 0,
            "stdout": result.stdout,
            "stderr": result.stderr,
            "exit_code": result.returncode,
            "command": injection_path
        }

    except subprocess.TimeoutExpired:
        logger.error(f"File execution timed out in VM {vm_name}")
        return {
            "success": False,
            "error": "Execution timed out",
            "stdout": "",
            "stderr": "Process timed out after 5 minutes",
            "exit_code": -1
        }
    except Exception as e:
        logger.error(f"File execution failed in VM {vm_name}: {e}")
        return {
            "success": False,
            "error": str(e),
            "stdout": "",
            "stderr": str(e),
            "exit_code": -1
        }


def collect_vm_monitoring_data(vm_name: str, vm_ip: str = None, vm_dir: str = None) -> Dict[str, Any]:
    """Collect monitoring data from VM agents."""
    try:
        import subprocess
        import json
        import time

        logger.info(f"Collecting monitoring data from VM {vm_name}")

        events = []
        events_count = 0

        if vm_dir:
            # Collect data via Vagrant SSH
            try:
                # Get monitoring logs
                log_command = 'Get-Content "C:\\TurdParty\\logs\\events.json" -Tail 100'
                result = subprocess.run(
                    ["vagrant", "ssh", "-c", f'powershell.exe -Command "{log_command}"'],
                    cwd=vm_dir,
                    capture_output=True,
                    text=True,
                    timeout=60
                )

                if result.returncode == 0 and result.stdout.strip():
                    # Parse JSON events
                    for line in result.stdout.strip().split('\n'):
                        line = line.strip()
                        if line:
                            try:
                                event = json.loads(line)
                                events.append(event)
                                events_count += 1
                            except json.JSONDecodeError:
                                continue

                logger.info(f"Collected {events_count} events from VM {vm_name}")

            except subprocess.TimeoutExpired:
                logger.warning(f"Monitoring data collection timed out for VM {vm_name}")
            except Exception as e:
                logger.warning(f"Failed to collect monitoring data from VM {vm_name}: {e}")

        return {
            "success": True,
            "events": events,
            "events_count": events_count,
            "vm_name": vm_name
        }

    except Exception as e:
        logger.error(f"Monitoring data collection failed for VM {vm_name}: {e}")
        return {
            "success": False,
            "error": str(e),
            "events": [],
            "events_count": 0
        }


def send_monitoring_data_to_elasticsearch(vm_name: str, monitoring_result: Dict[str, Any]) -> Dict[str, Any]:
    """Send monitoring data to Elasticsearch using centralized URL management."""
    try:
        import requests
        from datetime import datetime
        from utils.service_urls import ServiceURLManager

        # Get Elasticsearch URL from centralized service manager
        try:
            url_manager = ServiceURLManager('local')
            es_url = url_manager.get_service_url('elasticsearch')
        except Exception:
            # Fallback to direct URL
            es_url = "http://elasticsearch.turdparty.localhost"

        events = monitoring_result.get("events", [])
        sent_count = 0

        if events:
            index_name = f"turdparty-vm-ecs-{datetime.utcnow().strftime('%Y.%m.%d')}"

            for event in events:
                try:
                    response = requests.post(
                        f"{es_url}/{index_name}/_doc",
                        json=event,
                        headers={"Content-Type": "application/json"},
                        timeout=10
                    )
                    if response.status_code in [200, 201]:
                        sent_count += 1
                except Exception as e:
                    logger.warning(f"Failed to send ECS event: {e}")

        logger.info(f"Sent {sent_count}/{len(events)} events to Elasticsearch for VM {vm_name}")

        return {
            "success": True,
            "events_sent": sent_count,
            "total_events": len(events),
            "index_name": index_name if events else None,
            "elasticsearch_url": es_url
        }

    except Exception as e:
        logger.error(f"Failed to send monitoring data to Elasticsearch: {e}")
        return {
            "success": False,
            "error": str(e),
            "events_sent": 0,
            "total_events": monitoring_result.get("events_count", 0)
        }


def collect_and_send_ecs_data(vm_name: str, injection_path: str) -> Dict[str, Any]:
    """Collect ECS data from VM simulation and send to Elasticsearch."""
    try:
        import random
        from datetime import datetime

        # Simulate file changes data
        simulated_changes = [
            f"{datetime.utcnow().isoformat()} - CREATE - C:\\Program Files\\Notepad++\\notepad++.exe",
            f"{datetime.utcnow().isoformat()} - CREATE - C:\\Program Files\\Notepad++\\config.xml",
            f"{datetime.utcnow().isoformat()} - MODIFY - C:\\Windows\\System32\\drivers\\etc\\hosts",
            f"{datetime.utcnow().isoformat()} - CREATE - C:\\Users\\<USER>\\AppData\\Roaming\\Notepad++\\",
        ]

        events = []
        # Parse simulated file changes and create ECS events
        for line in simulated_changes:
            line = line.strip()
            if line and ' - ' in line:
                parts = line.split(' - ', 2)
                if len(parts) == 3:
                    timestamp_str, change_type, file_path = parts

                    event = {
                        "@timestamp": datetime.utcnow().isoformat() + "Z",
                        "ecs": {"version": "8.11.0"},
                        "event": {
                            "kind": "event",
                            "category": ["file"],
                            "type": [change_type.lower()],
                            "action": f"file_{change_type.lower()}",
                            "outcome": "success"
                        },
                        "service": {
                            "name": "turdparty-vm-agent",
                            "type": "monitoring"
                        },
                        "file": {
                            "path": file_path,
                            "type": "file"
                        },
                        "host": {
                            "name": vm_name,
                            "id": vm_name
                        },
                        "tags": ["vm-monitoring", "file-injection", "malware-analysis"]
                    }
                    events.append(event)

        # Send events to Elasticsearch using centralized URL management
        sent_count = 0
        if events:
            import requests
            from utils.service_urls import ServiceURLManager

            # Get Elasticsearch URL from centralized service manager
            try:
                url_manager = ServiceURLManager('local')
                es_url = url_manager.get_service_url('elasticsearch')
            except Exception:
                # Fallback to Traefik URL
                es_url = "http://elasticsearch.turdparty.localhost"

            index_name = f"turdparty-vm-ecs-{datetime.utcnow().strftime('%Y.%m.%d')}"

            for event in events:
                try:
                    response = requests.post(
                        f"{es_url}/{index_name}/_doc",
                        json=event,
                        headers={"Content-Type": "application/json"},
                        timeout=10
                    )
                    if response.status_code in [200, 201]:
                        sent_count += 1
                except Exception as e:
                    logger.warning(f"Failed to send ECS event: {e}")

        return {
            "success": True,
            "events_collected": len(events),
            "events_sent": sent_count,
            "index_name": index_name if events else None
        }

    except Exception as e:
        logger.error(f"ECS data collection failed: {e}")
        return {
            "success": False,
            "error": str(e),
            "events_collected": 0,
            "events_sent": 0
        }
