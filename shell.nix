{ pkgs ? import <nixpkgs> { config.allowUnfree = true; } }:

pkgs.mkShell {
  name = "turdparty-dev-shell";
  
  buildInputs = with pkgs; [
    # Shell and terminal tools
    zsh
    starship  # Modern shell prompt
    
    # Core development tools
    git
    git-lfs
    gh  # GitHub CLI
    
    # Python development
    python312
    python312Packages.pip
    python312Packages.virtualenv
    poetry
    
    # Node.js for frontend
    nodejs_20
    yarn
    
    # Database tools
    postgresql_15
    redis
    
    # Container and orchestration
    docker
    docker-compose

    # VM and infrastructure tools
    packer
    qemu
    libvirt
    xorriso  # ISO creation for Packer CD files
    
    # Testing and quality tools
    ruff
    mypy
    bandit

    # Pre-commit hooks
    pre-commit

    # Core application dependencies
    python312Packages.fastapi
    python312Packages.uvicorn
    python312Packages.pydantic
    python312Packages.httpx
    python312Packages.aiofiles
    python312Packages.python-multipart
    python312Packages.minio
    python312Packages.elasticsearch
    python312Packages.redis
    python312Packages.celery
    python312Packages.sqlalchemy
    python312Packages.alembic
    python312Packages.psycopg2

    # Advanced testing tools
    python312Packages.pytest
    python312Packages.pytest-asyncio
    python312Packages.pytest-cov
    python312Packages.pytest-html
    python312Packages.pytest-benchmark
    python312Packages.hypothesis
    python312Packages.factory-boy
    python312Packages.faker
    python312Packages.pytest-xdist
    python312Packages.pytest-mock
    python312Packages.pytest-timeout
    python312Packages.playwright  # E2E testing framework
    python312Packages.testcontainers  # Container testing framework

    # Additional testing dependencies
    python312Packages.selenium  # Web automation testing
    python312Packages.behave    # BDD testing framework
    python312Packages.beautifulsoup4  # HTML parsing (bs4)
    python312Packages.lxml      # XML/HTML processing
    python312Packages.pillow    # Image processing (PIL)

    # Missing test dependencies (available in nixpkgs)
    python312Packages.asyncpg
    python312Packages.structlog
    python312Packages.chardet     # Character encoding detection
    python312Packages.pytz        # Timezone handling
    python312Packages.wheel       # Python wheel format

    # Additional dependencies for API
    python312Packages.python-logstash

    # Integration test dependencies
    python312Packages.httpx
    python312Packages.blake3
    python312Packages.minio
    python312Packages.elasticsearch
    python312Packages.python-dotenv
    python312Packages.grpcio
    python312Packages.grpcio-tools

    # VM monitoring dependencies
    python312Packages.psutil
    python312Packages.docker
    python312Packages.websockets
    python312Packages.aiohttp  # Required by elasticsearch async client

    # VM file injection dependencies
    python312Packages.paramiko  # SSH/SFTP for file transfer
    python312Packages.pywinrm   # Windows Remote Management

    # CLI dependencies for sexy TurdParty CLI
    python312Packages.click
    python312Packages.rich
    python312Packages.requests
    
    # Performance and monitoring
    htop
    iotop
    nethogs
    
    # File and text processing
    jq
    yq-go
    ripgrep
    fd
    bat
    eza  # Modern replacement for exa
    gnugrep  # GNU grep with extended features
    
    # Network tools
    curl
    wget
    httpie
    
    # Archive and compression
    unzip
    zip
    gzip
    gnutar
    p7zip  # 7zip for VirtIO driver extraction
    
    # Development utilities
    fzf  # Fuzzy finder
    mc   # Midnight commander
    tree
    watch
    tmux
    
    # Security tools
    gnupg
    openssh
    
    # Build tools
    gnumake
    gcc
    
    # Documentation
    pandoc
    
    # Linting and formatting
    shellcheck
    shfmt
    
    # Monitoring and debugging
    strace
    lsof
    tcpdump
    
    # Math and calculations
    bc
    
    # Text editors (backup options)
    vim
    nano

    # Playwright browser dependencies
    glib
    gobject-introspection
    nss
    nspr
    dbus
    atk
    at-spi2-atk
    cups
    expat
    xorg.libX11
    xorg.libXcomposite
    xorg.libXdamage
    xorg.libXext
    xorg.libXfixes
    xorg.libXrandr
    mesa  # provides libgbm
    xorg.libxcb
    libxkbcommon
    pango
    cairo
    systemd  # provides libudev
    alsa-lib
    at-spi2-core

    # Additional Playwright dependencies
    gtk3
    gdk-pixbuf
    fontconfig
    freetype
    harfbuzz
    libdrm
    xorg.libXScrnSaver
    xorg.libXtst
    xorg.libXi
    xorg.libXcursor
    xorg.libXrender
    xorg.libXinerama
    libGL
    libGLU

    # Documentation build tools
    python312Packages.sphinx
    python312Packages.sphinx-rtd-theme
    python312Packages.myst-parser
    python312Packages.sphinx-autobuild

    # Build report generation dependencies
    python312Packages.jinja2
    python312Packages.matplotlib
    python312Packages.seaborn
  ];

  shellHook = ''
    # Configure zsh with git branch in prompt
    export SHELL="${pkgs.zsh}/bin/zsh"
    export ZDOTDIR="$PWD/.nix-zsh"

    # Only run setup if not already configured or if TURDPARTY_VERBOSE is set
    if [ ! -f "$ZDOTDIR/.turdparty_configured" ] || [ -n "$TURDPARTY_VERBOSE" ]; then

      # Only show startup info if TURDPARTY_VERBOSE is set
      if [ -n "$TURDPARTY_VERBOSE" ] && [ -t 0 ] && [ -t 1 ] && [ -z "$ZSH_VERSION" ]; then
        echo "🚀 TurdParty Development Environment"
        echo "📁 Project: $(basename $(pwd))"
        echo "🐍 Python: $(python --version)"
        echo "🐚 Shell: zsh with git branch display"
        echo ""
      fi

      # Set up custom zsh configuration with git branch
      mkdir -p "$ZDOTDIR"

      # Create zsh configuration with git branch prompt (only if not exists or verbose)
      if [ ! -f "$ZDOTDIR/.zshrc" ] || [ -n "$TURDPARTY_VERBOSE" ]; then
        cat > "$ZDOTDIR/.zshrc" << 'EOF'
# Enable git prompt
autoload -Uz vcs_info
precmd() { vcs_info }
zstyle ':vcs_info:git:*' formats ' (%b)'
setopt PROMPT_SUBST

# Custom prompt with git branch
PROMPT='💩🎉 %F{cyan}%n@%m%f:%F{yellow}%~%f%F{green}''${vcs_info_msg_0_}%f %F{magenta}$%f '

# Enable command completion
autoload -U compinit
compinit

# History configuration
HISTFILE="$ZDOTDIR/.zsh_history"
HISTSIZE=10000
SAVEHIST=10000
setopt SHARE_HISTORY
setopt HIST_IGNORE_DUPS
setopt HIST_IGNORE_ALL_DUPS

# Enable useful zsh features
setopt AUTO_CD
setopt CORRECT
setopt EXTENDED_GLOB

# Aliases for TurdParty development
alias ll='eza -la --git'
alias la='eza -la'
alias ls='eza'
alias cat='bat'
alias grep='rg'
alias find='fd'
alias ps='ps aux'
alias df='df -h'
alias du='du -h'
alias free='free -h'

# Git aliases
alias gs='git status'
alias ga='git add'
alias gc='git commit'
alias gp='git push'
alias gl='git log --oneline'
alias gb='git branch'
alias gco='git checkout'

# TurdParty specific aliases
alias tp-status='./scripts/turdparty status'
alias tp-monitor='./scripts/turdparty monitor'
alias tp-start='./scripts/turdparty start'
alias tp-test='./scripts/run-parallel-tests.sh'
alias tp-logs='docker-compose logs -f'

# Docker aliases
alias dc='docker-compose'
alias dcu='docker-compose up -d'
alias dcd='docker-compose down'
alias dcl='docker-compose logs -f'
alias dps='docker ps'
alias di='docker images'

# Zsh configured silently - only echo if TURDPARTY_VERBOSE is set
if [ -n "$TURDPARTY_VERBOSE" ]; then
  echo "🎉 Zsh configured with git branch display and TurdParty aliases!"
fi
EOF
      fi

      # Create necessary directories (only once)
      mkdir -p logs data/uploads data/temp_files test-results coverage-archive reports

      # Mark as configured
      touch "$ZDOTDIR/.turdparty_configured"

      # Only show help if TURDPARTY_VERBOSE is set
      if [ -n "$TURDPARTY_VERBOSE" ] && [ -t 0 ] && [ -t 1 ] && [ -z "$ZSH_VERSION" ]; then
        echo "🔧 Quick Commands:"
        echo "  tp-test                    # Run parallel test suite"
        echo "  tp-status                  # Service dashboard"
        echo "  tp-monitor                 # Real-time monitoring"
        echo ""
      fi
    fi

    # Auto-start zsh only for interactive shells (not when running commands)
    # Check if we're in an interactive session and not already in zsh
    if [ -f "$ZDOTDIR/.zshrc" ] && [ -z "$ZSH_VERSION" ] && [ -t 0 ] && [ -t 1 ]; then
      # Only auto-start if no command was passed to nix-shell
      case "$-" in
        *i*) exec ${pkgs.zsh}/bin/zsh ;;
      esac
    fi

    # Set up Python environment
    export PYTHONPATH="$PWD:$PWD/services/api:$PYTHONPATH"

    # Install additional Sphinx extensions not available in nixpkgs
    if [ ! -f "$PWD/.sphinx_extensions_installed" ]; then
      echo "📚 Installing additional Sphinx extensions..."
      pip install --break-system-packages --quiet \
        sphinxcontrib-mermaid \
        sphinx-copybutton \
        sphinx-tabs \
        sphinx-design \
        2>/dev/null || true
      touch "$PWD/.sphinx_extensions_installed"
    fi

    # Set up development environment variables
    export DEVELOPMENT=true
    export DEBUG=true
    export LOG_LEVEL=DEBUG
    export TURDPARTY_ENV=development
    export COMPOSE_PROJECT_NAME=turdpartycollab
    export DOCKER_BUILDKIT=1
    export COVERAGE_THRESHOLD=80
    export PARALLEL_WORKERS=4

    # Set up library paths for Playwright browser dependencies
    export LD_LIBRARY_PATH="${pkgs.lib.makeLibraryPath [
      pkgs.glib
      pkgs.nss
      pkgs.nspr
      pkgs.dbus
      pkgs.atk
      pkgs.at-spi2-atk
      pkgs.cups
      pkgs.expat
      pkgs.xorg.libX11
      pkgs.xorg.libXcomposite
      pkgs.xorg.libXdamage
      pkgs.xorg.libXext
      pkgs.xorg.libXfixes
      pkgs.xorg.libXrandr
      pkgs.mesa
      pkgs.xorg.libxcb
      pkgs.libxkbcommon
      pkgs.pango
      pkgs.cairo
      pkgs.systemd
      pkgs.alsa-lib
      pkgs.at-spi2-core
      pkgs.gtk3
      pkgs.gdk-pixbuf
      pkgs.fontconfig
      pkgs.freetype
      pkgs.harfbuzz
      pkgs.libdrm
      pkgs.xorg.libXScrnSaver
      pkgs.xorg.libXtst
      pkgs.xorg.libXi
      pkgs.xorg.libXcursor
      pkgs.xorg.libXrender
      pkgs.xorg.libXinerama
      pkgs.libGL
      pkgs.libGLU
    ]}:$LD_LIBRARY_PATH"

    # Install Playwright browsers if not already installed
    if [ ! -f "$PWD/.playwright_browsers_installed" ]; then
      echo "🎭 Installing Playwright browsers..."
      python -m playwright install chromium --with-deps 2>/dev/null || true
      touch "$PWD/.playwright_browsers_installed"
    fi


  '';

  # Environment variables
  NIX_SHELL_PRESERVE_PROMPT = "1";
  
  # Shell configuration
  SHELL = "${pkgs.zsh}/bin/zsh";
  
  # Development tools configuration
  EDITOR = "vim";
  PAGER = "bat";
  
  # Python configuration
  PYTHONDONTWRITEBYTECODE = "1";
  PYTHONUNBUFFERED = "1";
  
  # Node.js configuration
  NODE_ENV = "development";
  
  # Git configuration
  GIT_EDITOR = "vim";
}
