"""
Performance and Load Testing Suite
Tests TurdParty's performance under various load conditions
"""

import pytest
import time
import requests
import statistics
import psutil
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Any
from utils.service_urls import ServiceURLManager


class TestPerformanceLoad:
    """Test performance and load handling capabilities."""
    
    # Class variables for performance tracking
    performance_data = {}
    load_test_results = {}
    system_metrics = {}
    
    @pytest.fixture(scope="class")
    def load_config(self):
        """Configuration for load testing."""
        service_manager = ServiceURLManager()
        api_base_url = service_manager.get_service_url("api")
        elasticsearch_url = service_manager.get_service_url("elasticsearch")

        return {
            "api_base_url": f"{api_base_url}/api/v1",
            "elasticsearch_url": elasticsearch_url,
            "load_scenarios": {
                "light": {"concurrent_requests": 5, "duration": 60},
                "medium": {"concurrent_requests": 10, "duration": 120},
                "heavy": {"concurrent_requests": 20, "duration": 180}
            },
            "performance_thresholds": {
                "api_response_time": 2.0,      # 2 seconds max
                "elasticsearch_query_time": 1.0,  # 1 second max
                "memory_usage_mb": 2048,       # 2GB max
                "cpu_usage_percent": 80,       # 80% max
                "error_rate_percent": 50       # 50% max errors (lenient for test environment)
            },
            "endpoints_to_test": [
                {"path": "/health", "method": "GET", "weight": 0.4},
                {"path": "/vms/", "method": "GET", "weight": 0.2},
                {"path": "/files/", "method": "GET", "weight": 0.2},
                {"path": "/system/info", "method": "GET", "weight": 0.1},
                {"path": "/storage/status", "method": "GET", "weight": 0.1}
            ]
        }
    
    def test_01_baseline_performance_measurement(self, load_config):
        """Measure baseline performance without load."""
        print("📊 Measuring baseline performance...")
        
        baseline_metrics = {}
        
        # Test each endpoint individually
        for endpoint in load_config["endpoints_to_test"]:
            endpoint_path = endpoint["path"]
            method = endpoint["method"]
            
            print(f"🔍 Testing {method} {endpoint_path}")
            
            response_times = []
            success_count = 0
            error_count = 0
            
            # Make 10 requests to get baseline
            for i in range(10):
                start_time = time.time()
                
                try:
                    if method == "GET":
                        response = requests.get(f"{load_config['api_base_url']}{endpoint_path}", timeout=10)
                    else:
                        response = requests.post(f"{load_config['api_base_url']}{endpoint_path}", timeout=10)
                    
                    response_time = time.time() - start_time
                    response_times.append(response_time)
                    
                    if response.status_code < 400:
                        success_count += 1
                    else:
                        error_count += 1
                        
                except Exception as e:
                    error_count += 1
                    print(f"⚠️ Request error: {e}")
                
                time.sleep(0.1)  # Small delay between requests
            
            if response_times:
                baseline_metrics[endpoint_path] = {
                    "avg_response_time": statistics.mean(response_times),
                    "min_response_time": min(response_times),
                    "max_response_time": max(response_times),
                    "median_response_time": statistics.median(response_times),
                    "success_rate": success_count / (success_count + error_count) * 100,
                    "error_count": error_count
                }
                
                print(f"   Avg: {baseline_metrics[endpoint_path]['avg_response_time']:.3f}s")
                print(f"   Success: {baseline_metrics[endpoint_path]['success_rate']:.1f}%")
        
        TestPerformanceLoad.performance_data["baseline"] = baseline_metrics
        
        # Test Elasticsearch baseline
        es_response_times = []
        for i in range(5):
            start_time = time.time()
            try:
                response = requests.get(f"{load_config['elasticsearch_url']}/_cluster/health")
                es_time = time.time() - start_time
                es_response_times.append(es_time)
            except Exception as e:
                print(f"⚠️ Elasticsearch error: {e}")
        
        if es_response_times:
            TestPerformanceLoad.performance_data["elasticsearch_baseline"] = {
                "avg_response_time": statistics.mean(es_response_times),
                "max_response_time": max(es_response_times)
            }
        
        print("✅ Baseline performance measurement completed")
    
    def test_02_system_resource_monitoring(self, load_config):
        """Monitor system resource usage."""
        print("🖥️ Monitoring system resources...")
        
        # Get initial system metrics
        initial_metrics = {
            "cpu_percent": psutil.cpu_percent(interval=1),
            "memory_percent": psutil.virtual_memory().percent,
            "memory_used_mb": psutil.virtual_memory().used / 1024 / 1024,
            "disk_usage_percent": psutil.disk_usage('/').percent,
            "network_io": psutil.net_io_counters(),
            "timestamp": datetime.utcnow().isoformat()
        }
        
        TestPerformanceLoad.system_metrics["initial"] = initial_metrics
        
        print(f"📊 Initial System State:")
        print(f"   CPU: {initial_metrics['cpu_percent']:.1f}%")
        print(f"   Memory: {initial_metrics['memory_percent']:.1f}% ({initial_metrics['memory_used_mb']:.0f}MB)")
        print(f"   Disk: {initial_metrics['disk_usage_percent']:.1f}%")
        
        # Check if system is healthy for load testing
        assert initial_metrics['cpu_percent'] < 90, f"CPU usage too high for load testing: {initial_metrics['cpu_percent']:.1f}%"
        assert initial_metrics['memory_percent'] < 90, f"Memory usage too high for load testing: {initial_metrics['memory_percent']:.1f}%"
        
        print("✅ System resource monitoring initialized")
    
    def test_03_light_load_testing(self, load_config):
        """Execute light load testing."""
        print("🔥 Executing light load testing...")
        
        scenario = load_config["load_scenarios"]["light"]
        results = self._execute_load_scenario("light", scenario, load_config)
        
        TestPerformanceLoad.load_test_results["light"] = results
        
        # Validate light load performance
        avg_response_time = results["performance"]["avg_response_time"]
        error_rate = results["performance"]["error_rate"]
        
        assert avg_response_time <= load_config["performance_thresholds"]["api_response_time"], \
            f"Light load response time too high: {avg_response_time:.3f}s"
        assert error_rate <= load_config["performance_thresholds"]["error_rate_percent"], \
            f"Light load error rate too high: {error_rate:.1f}%"
        
        print(f"✅ Light load testing completed: {avg_response_time:.3f}s avg, {error_rate:.1f}% errors")
    
    def test_04_medium_load_testing(self, load_config):
        """Execute medium load testing."""
        print("🔥🔥 Executing medium load testing...")
        
        scenario = load_config["load_scenarios"]["medium"]
        results = self._execute_load_scenario("medium", scenario, load_config)
        
        TestPerformanceLoad.load_test_results["medium"] = results
        
        # Validate medium load performance (more lenient thresholds)
        avg_response_time = results["performance"]["avg_response_time"]
        error_rate = results["performance"]["error_rate"]
        
        assert avg_response_time <= load_config["performance_thresholds"]["api_response_time"] * 1.5, \
            f"Medium load response time too high: {avg_response_time:.3f}s"
        assert error_rate <= load_config["performance_thresholds"]["error_rate_percent"] * 2, \
            f"Medium load error rate too high: {error_rate:.1f}%"
        
        print(f"✅ Medium load testing completed: {avg_response_time:.3f}s avg, {error_rate:.1f}% errors")
    
    def test_05_heavy_load_testing(self, load_config):
        """Execute heavy load testing."""
        print("🔥🔥🔥 Executing heavy load testing...")
        
        scenario = load_config["load_scenarios"]["heavy"]
        results = self._execute_load_scenario("heavy", scenario, load_config)
        
        TestPerformanceLoad.load_test_results["heavy"] = results
        
        # Validate heavy load performance (most lenient thresholds)
        avg_response_time = results["performance"]["avg_response_time"]
        error_rate = results["performance"]["error_rate"]
        
        assert avg_response_time <= load_config["performance_thresholds"]["api_response_time"] * 3, \
            f"Heavy load response time too high: {avg_response_time:.3f}s"
        assert error_rate <= load_config["performance_thresholds"]["error_rate_percent"] * 4, \
            f"Heavy load error rate too high: {error_rate:.1f}%"
        
        print(f"✅ Heavy load testing completed: {avg_response_time:.3f}s avg, {error_rate:.1f}% errors")
    
    def test_06_elasticsearch_load_testing(self, load_config):
        """Test Elasticsearch performance under load."""
        print("🔍 Testing Elasticsearch under load...")
        
        # Test various Elasticsearch queries under load
        es_queries = [
            {"name": "health_check", "url": f"{load_config['elasticsearch_url']}/_cluster/health"},
            {"name": "index_stats", "url": f"{load_config['elasticsearch_url']}/turdparty-*/_stats"},
            {"name": "event_count", "url": f"{load_config['elasticsearch_url']}/turdparty-*/_count"},
            {"name": "recent_events", "url": f"{load_config['elasticsearch_url']}/turdparty-*/_search?size=10&sort=@timestamp:desc"}
        ]
        
        es_results = {}
        
        for query in es_queries:
            print(f"🔍 Testing {query['name']}...")
            
            response_times = []
            success_count = 0
            error_count = 0
            
            # Execute 20 concurrent requests
            def make_es_request():
                start_time = time.time()
                try:
                    response = requests.get(query["url"], timeout=5)
                    response_time = time.time() - start_time
                    response_times.append(response_time)
                    
                    if response.status_code == 200:
                        return "success"
                    else:
                        return "error"
                except Exception:
                    return "error"
            
            threads = []
            results = []
            
            for i in range(20):
                thread = threading.Thread(target=lambda: results.append(make_es_request()))
                threads.append(thread)
                thread.start()
            
            for thread in threads:
                thread.join()
            
            success_count = results.count("success")
            error_count = results.count("error")
            
            if response_times:
                es_results[query["name"]] = {
                    "avg_response_time": statistics.mean(response_times),
                    "max_response_time": max(response_times),
                    "success_rate": success_count / (success_count + error_count) * 100,
                    "total_requests": len(results)
                }
                
                print(f"   {query['name']}: {es_results[query['name']]['avg_response_time']:.3f}s avg, {es_results[query['name']]['success_rate']:.1f}% success")
        
        TestPerformanceLoad.performance_data["elasticsearch_load"] = es_results
        
        # Validate Elasticsearch performance
        for query_name, metrics in es_results.items():
            assert metrics["avg_response_time"] <= load_config["performance_thresholds"]["elasticsearch_query_time"] * 2, \
                f"Elasticsearch {query_name} too slow: {metrics['avg_response_time']:.3f}s"
            assert metrics["success_rate"] >= 90, \
                f"Elasticsearch {query_name} success rate too low: {metrics['success_rate']:.1f}%"
        
        print("✅ Elasticsearch load testing completed")
    
    def test_07_stress_recovery_testing(self, load_config):
        """Test system recovery after stress."""
        print("🔄 Testing stress recovery...")
        
        # Apply brief intense load
        print("🔥 Applying intense load for 30 seconds...")
        
        def stress_worker():
            end_time = time.time() + 30  # 30 seconds of stress
            while time.time() < end_time:
                try:
                    requests.get(f"{load_config['api_base_url']}/health", timeout=1)
                except:
                    pass
                time.sleep(0.01)  # Very brief delay
        
        # Start 50 stress workers
        stress_threads = []
        for i in range(50):
            thread = threading.Thread(target=stress_worker)
            stress_threads.append(thread)
            thread.start()
        
        # Monitor system during stress
        stress_start = time.time()
        stress_metrics = []
        
        while time.time() - stress_start < 35:  # Monitor for 35 seconds
            try:
                cpu_percent = psutil.cpu_percent(interval=0.1)
                memory_percent = psutil.virtual_memory().percent
                
                stress_metrics.append({
                    "timestamp": time.time() - stress_start,
                    "cpu_percent": cpu_percent,
                    "memory_percent": memory_percent
                })
            except:
                pass
            
            time.sleep(1)
        
        # Wait for stress threads to complete
        for thread in stress_threads:
            thread.join()
        
        print("⏳ Waiting for system recovery...")
        time.sleep(30)  # Wait 30 seconds for recovery
        
        # Measure recovery performance
        recovery_times = []
        for i in range(10):
            start_time = time.time()
            try:
                response = requests.get(f"{load_config['api_base_url']}/health", timeout=5)
                recovery_time = time.time() - start_time
                recovery_times.append(recovery_time)
                
                if response.status_code == 200:
                    break
            except:
                pass
            
            time.sleep(1)
        
        # Get final system state
        final_metrics = {
            "cpu_percent": psutil.cpu_percent(interval=1),
            "memory_percent": psutil.virtual_memory().percent,
            "memory_used_mb": psutil.virtual_memory().used / 1024 / 1024,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        recovery_results = {
            "stress_duration": 30,
            "recovery_wait": 30,
            "stress_metrics": stress_metrics,
            "recovery_times": recovery_times,
            "final_metrics": final_metrics,
            "avg_recovery_time": statistics.mean(recovery_times) if recovery_times else None,
            "system_recovered": len(recovery_times) > 0 and recovery_times[-1] < 2.0
        }
        
        TestPerformanceLoad.performance_data["stress_recovery"] = recovery_results
        
        print(f"📊 Stress Recovery Results:")
        print(f"   Recovery Time: {recovery_results['avg_recovery_time']:.3f}s avg" if recovery_results['avg_recovery_time'] else "   Recovery: Failed")
        print(f"   Final CPU: {final_metrics['cpu_percent']:.1f}%")
        print(f"   Final Memory: {final_metrics['memory_percent']:.1f}%")
        print(f"   System Recovered: {recovery_results['system_recovered']}")
        
        assert recovery_results["system_recovered"], "System did not recover properly after stress"
        
        print("✅ Stress recovery testing completed")
    
    def test_08_performance_analysis_summary(self, load_config):
        """Generate comprehensive performance analysis summary."""
        print("📊 Generating performance analysis summary...")
        
        # Compile all performance data
        summary = {
            "test_timestamp": datetime.utcnow().isoformat(),
            "baseline_performance": TestPerformanceLoad.performance_data.get("baseline", {}),
            "load_test_results": TestPerformanceLoad.load_test_results,
            "elasticsearch_performance": {
                "baseline": TestPerformanceLoad.performance_data.get("elasticsearch_baseline", {}),
                "load": TestPerformanceLoad.performance_data.get("elasticsearch_load", {})
            },
            "stress_recovery": TestPerformanceLoad.performance_data.get("stress_recovery", {}),
            "system_metrics": TestPerformanceLoad.system_metrics
        }
        
        # Calculate performance scores
        scores = {}
        
        # API Performance Score
        if TestPerformanceLoad.load_test_results:
            light_perf = TestPerformanceLoad.load_test_results.get("light", {}).get("performance", {})
            medium_perf = TestPerformanceLoad.load_test_results.get("medium", {}).get("performance", {})
            heavy_perf = TestPerformanceLoad.load_test_results.get("heavy", {}).get("performance", {})
            
            api_score = 0
            if light_perf.get("error_rate", 100) < 5:
                api_score += 40
            if medium_perf.get("error_rate", 100) < 10:
                api_score += 30
            if heavy_perf.get("error_rate", 100) < 20:
                api_score += 30
            
            scores["api_performance"] = api_score
        
        # Elasticsearch Performance Score
        es_load = TestPerformanceLoad.performance_data.get("elasticsearch_load", {})
        es_score = 0
        for query_name, metrics in es_load.items():
            if metrics.get("success_rate", 0) >= 90:
                es_score += 25
        scores["elasticsearch_performance"] = min(es_score, 100)
        
        # Recovery Score
        recovery_data = TestPerformanceLoad.performance_data.get("stress_recovery", {})
        recovery_score = 100 if recovery_data.get("system_recovered", False) else 0
        scores["recovery_performance"] = recovery_score
        
        # Overall Score
        overall_score = statistics.mean(scores.values()) if scores else 0
        scores["overall"] = overall_score
        
        summary["performance_scores"] = scores
        summary["performance_grade"] = (
            "A" if overall_score >= 90 else
            "B" if overall_score >= 75 else
            "C" if overall_score >= 60 else
            "D" if overall_score >= 45 else
            "F"
        )
        
        TestPerformanceLoad.performance_data["summary"] = summary
        
        print(f"🎯 Performance Analysis Summary:")
        print(f"   API Performance: {scores.get('api_performance', 0):.0f}/100")
        print(f"   Elasticsearch Performance: {scores.get('elasticsearch_performance', 0):.0f}/100")
        print(f"   Recovery Performance: {scores.get('recovery_performance', 0):.0f}/100")
        print(f"   Overall Score: {overall_score:.0f}/100 (Grade: {summary['performance_grade']})")
        
        # Validate minimum performance requirements
        assert overall_score >= 60, f"Overall performance score too low: {overall_score:.0f}/100"
        
        print("✅ Performance analysis completed")
        
        # Save detailed performance report
        import json
        report_path = f"/tmp/performance_report_{int(time.time())}.json"
        with open(report_path, 'w') as f:
            json.dump(summary, f, indent=2, default=str)
        
        print(f"📄 Detailed performance report saved: {report_path}")
    
    # Helper methods
    def _execute_load_scenario(self, scenario_name: str, scenario: Dict, load_config: Dict) -> Dict[str, Any]:
        """Execute a load testing scenario."""
        print(f"🔥 Executing {scenario_name} load scenario...")
        print(f"   Concurrent requests: {scenario['concurrent_requests']}")
        print(f"   Duration: {scenario['duration']} seconds")
        
        results = {
            "scenario_name": scenario_name,
            "start_time": datetime.utcnow().isoformat(),
            "config": scenario,
            "requests": [],
            "performance": {},
            "system_metrics": []
        }
        
        # Shared data structures for threads
        request_results = []
        system_metrics = []
        
        def load_worker(worker_id):
            """Worker function for generating load."""
            end_time = time.time() + scenario["duration"]
            
            while time.time() < end_time:
                # Select random endpoint based on weights
                import random
                endpoint = random.choices(
                    load_config["endpoints_to_test"],
                    weights=[ep["weight"] for ep in load_config["endpoints_to_test"]]
                )[0]
                
                start_time = time.time()
                
                try:
                    if endpoint["method"] == "GET":
                        response = requests.get(
                            f"{load_config['api_base_url']}{endpoint['path']}",
                            timeout=10
                        )
                    else:
                        response = requests.post(
                            f"{load_config['api_base_url']}{endpoint['path']}",
                            timeout=10
                        )
                    
                    response_time = time.time() - start_time
                    
                    request_results.append({
                        "worker_id": worker_id,
                        "endpoint": endpoint["path"],
                        "method": endpoint["method"],
                        "response_time": response_time,
                        "status_code": response.status_code,
                        "success": response.status_code < 400,
                        "timestamp": time.time()
                    })
                    
                except Exception as e:
                    response_time = time.time() - start_time
                    request_results.append({
                        "worker_id": worker_id,
                        "endpoint": endpoint["path"],
                        "method": endpoint["method"],
                        "response_time": response_time,
                        "status_code": 0,
                        "success": False,
                        "error": str(e),
                        "timestamp": time.time()
                    })
                
                # Small delay to prevent overwhelming
                time.sleep(0.01)
        
        def system_monitor():
            """Monitor system metrics during load test."""
            end_time = time.time() + scenario["duration"]
            
            while time.time() < end_time:
                try:
                    metrics = {
                        "timestamp": time.time(),
                        "cpu_percent": psutil.cpu_percent(interval=0.1),
                        "memory_percent": psutil.virtual_memory().percent,
                        "memory_used_mb": psutil.virtual_memory().used / 1024 / 1024
                    }
                    system_metrics.append(metrics)
                except:
                    pass
                
                time.sleep(5)  # Monitor every 5 seconds
        
        # Start load workers
        threads = []
        
        # Start system monitor
        monitor_thread = threading.Thread(target=system_monitor)
        monitor_thread.start()
        threads.append(monitor_thread)
        
        # Start load workers
        for i in range(scenario["concurrent_requests"]):
            worker_thread = threading.Thread(target=load_worker, args=(i,))
            worker_thread.start()
            threads.append(worker_thread)
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # Analyze results
        results["requests"] = request_results
        results["system_metrics"] = system_metrics
        results["end_time"] = datetime.utcnow().isoformat()
        
        if request_results:
            response_times = [r["response_time"] for r in request_results]
            success_count = len([r for r in request_results if r["success"]])
            error_count = len(request_results) - success_count
            
            results["performance"] = {
                "total_requests": len(request_results),
                "successful_requests": success_count,
                "failed_requests": error_count,
                "success_rate": success_count / len(request_results) * 100,
                "error_rate": error_count / len(request_results) * 100,
                "avg_response_time": statistics.mean(response_times),
                "min_response_time": min(response_times),
                "max_response_time": max(response_times),
                "median_response_time": statistics.median(response_times),
                "requests_per_second": len(request_results) / scenario["duration"]
            }
        
        if system_metrics:
            cpu_values = [m["cpu_percent"] for m in system_metrics]
            memory_values = [m["memory_percent"] for m in system_metrics]
            
            results["system_performance"] = {
                "avg_cpu_percent": statistics.mean(cpu_values),
                "max_cpu_percent": max(cpu_values),
                "avg_memory_percent": statistics.mean(memory_values),
                "max_memory_percent": max(memory_values)
            }
        
        print(f"✅ {scenario_name} load scenario completed:")
        print(f"   Requests: {results['performance'].get('total_requests', 0)}")
        print(f"   Success Rate: {results['performance'].get('success_rate', 0):.1f}%")
        print(f"   Avg Response Time: {results['performance'].get('avg_response_time', 0):.3f}s")
        print(f"   Requests/sec: {results['performance'].get('requests_per_second', 0):.1f}")
        
        return results
