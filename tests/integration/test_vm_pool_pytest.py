#!/usr/bin/env python3
"""
🧪 Enhanced VM Pool Pytest Integration Tests

Pytest-compatible integration tests for the enhanced VM provisioning queue system.
These tests can be run with pytest for CI/CD integration.

Usage:
    pytest tests/integration/test_vm_pool_pytest.py -v
    pytest tests/integration/test_vm_pool_pytest.py::test_api_endpoints -v
    pytest tests/integration/test_vm_pool_pytest.py -k "priority" -v
"""

import asyncio
import json
import pytest
import time
import uuid
from datetime import datetime
from pathlib import Path
import sys

import httpx
import requests
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from utils.service_urls import ServiceURLManager


@pytest.fixture(scope="session")
def test_config():
    """Test configuration fixture."""
    url_manager = ServiceURLManager('local')
    return {
        "api_base": url_manager.get_service_url('api'),
        "elasticsearch_url": url_manager.get_service_url('elasticsearch'),
        "redis_url": "redis://localhost:6379/0",
        "db_url": "postgresql://postgres:postgres@localhost:5432/turdparty",
        "test_session_id": f"pytest-{int(time.time())}"
    }


@pytest.fixture(scope="session")
def db_session(test_config):
    """Database session fixture."""
    engine = create_engine(test_config["db_url"])
    SessionLocal = sessionmaker(bind=engine)
    session = SessionLocal()
    yield session
    session.close()


@pytest.mark.asyncio
async def test_api_endpoints(test_config):
    """Test enhanced VM pool API endpoints."""
    async with httpx.AsyncClient(timeout=30.0) as client:
        # Test pool status endpoint
        response = await client.get(f"{test_config['api_base']}/api/v1/pools/status")
        assert response.status_code == 200
        
        data = response.json()
        assert "data" in data
        assert "templates" in data["data"]
        
        # Test VM allocation endpoint
        allocation_request = {
            "priority": 3,
            "requester_id": f"pytest-{test_config['test_session_id']}",
            "timeout_seconds": 30,
            "metadata": {"test": True, "framework": "pytest"}
        }
        
        response = await client.post(
            f"{test_config['api_base']}/api/v1/pools/ubuntu:20.04/allocate",
            json=allocation_request
        )
        
        # Should either succeed or timeout gracefully
        assert response.status_code in [200, 201, 408, 504]


@pytest.mark.asyncio
async def test_celery_integration(test_config):
    """Test Celery task execution."""
    try:
        from services.workers.celery_app import app
        
        # Send a test task
        task = app.send_task(
            'services.workers.tasks.enhanced_vm_pool_manager.get_enhanced_pool_status',
            queue='pool_ops'
        )
        
        assert task.id is not None
        
        # Try to get result with timeout
        try:
            result = task.get(timeout=30)
            # If we get here, task executed successfully
            assert result is not None
        except Exception:
            # Task timeout is acceptable in test environment
            pass
            
    except ImportError:
        pytest.skip("Celery not available in test environment")


@pytest.mark.asyncio
async def test_database_operations(test_config, db_session):
    """Test database operations for enhanced VM pools."""
    # Test pool configurations
    result = db_session.execute(
        text("SELECT COUNT(*) FROM pool_configurations WHERE enabled = true")
    )
    config_count = result.scalar()
    assert config_count > 0, "No pool configurations found"
    
    # Test VM instances in enhanced pools
    result = db_session.execute(
        text("SELECT COUNT(*) FROM enhanced_resource_pools")
    )
    vm_count = result.scalar()
    assert vm_count >= 0, "Enhanced resource pools table not accessible"
    
    # Test allocation tracking with temporary data
    test_vm_id = str(uuid.uuid4())
    db_session.execute(
        text("""
            INSERT INTO enhanced_resource_pools 
            (id, vm_id, template, memory_mb, cpus, disk_gb, status, created_at)
            VALUES (:id, :vm_id, :template, :memory_mb, :cpus, :disk_gb, :status, :created_at)
        """),
        {
            "id": test_vm_id,
            "vm_id": f"pytest-vm-{test_config['test_session_id']}",
            "template": "test:pytest",
            "memory_mb": 1024,
            "cpus": 1,
            "disk_gb": 10,
            "status": "ready",
            "created_at": datetime.utcnow()
        }
    )
    db_session.commit()
    
    # Verify insertion
    result = db_session.execute(
        text("SELECT id FROM enhanced_resource_pools WHERE id = :id"),
        {"id": test_vm_id}
    )
    assert result.fetchone() is not None, "Test VM insertion failed"
    
    # Cleanup
    db_session.execute(
        text("DELETE FROM enhanced_resource_pools WHERE id = :id"),
        {"id": test_vm_id}
    )
    db_session.commit()


@pytest.mark.asyncio
async def test_elasticsearch_logging(test_config):
    """Test Elasticsearch logging integration."""
    async with httpx.AsyncClient(timeout=30.0) as client:
        # Check if Celery logs index exists
        today = datetime.now().strftime('%Y.%m.%d')
        index_name = f"ecs-turdparty-celery-{today}"
        
        response = await client.get(f"{test_config['elasticsearch_url']}/{index_name}")
        
        if response.status_code == 200:
            # Index exists, check for recent logs
            search_query = {
                "query": {
                    "range": {
                        "@timestamp": {
                            "gte": "now-1h"
                        }
                    }
                },
                "size": 1
            }
            
            response = await client.post(
                f"{test_config['elasticsearch_url']}/{index_name}/_search",
                json=search_query
            )
            
            assert response.status_code == 200
            data = response.json()
            
            # Check if we have recent logs
            hits = data.get("hits", {}).get("hits", [])
            if hits:
                # Verify ECS structure
                first_log = hits[0]["_source"]
                required_fields = ["@timestamp", "ecs", "event", "service"]
                
                for field in required_fields:
                    assert field in first_log, f"Missing ECS field: {field}"
        else:
            pytest.skip("Elasticsearch index not found - may be first run")


@pytest.mark.asyncio
@pytest.mark.parametrize("priority,priority_name", [
    (1, "critical"),
    (2, "high"),
    (3, "normal"),
    (4, "low")
])
async def test_priority_allocation(test_config, priority, priority_name):
    """Test priority-based VM allocation."""
    async with httpx.AsyncClient(timeout=60.0) as client:
        allocation_request = {
            "priority": priority,
            "requester_id": f"pytest-{priority_name}-{test_config['test_session_id']}",
            "timeout_seconds": 30,
            "metadata": {
                "test": True,
                "priority_test": priority_name,
                "framework": "pytest"
            }
        }
        
        start_time = time.time()
        response = await client.post(
            f"{test_config['api_base']}/api/v1/pools/ubuntu:20.04/allocate",
            json=allocation_request
        )
        allocation_time = time.time() - start_time
        
        # Should either succeed or timeout gracefully
        assert response.status_code in [200, 201, 408, 504]
        
        # Allocation should complete within reasonable time
        assert allocation_time < 35.0, f"Allocation took too long: {allocation_time:.2f}s"


@pytest.mark.asyncio
@pytest.mark.parametrize("template", [
    "ubuntu:20.04",
    "ubuntu:22.04",
    "alpine:latest"
])
async def test_multi_template_support(test_config, template):
    """Test multi-template VM allocation support."""
    async with httpx.AsyncClient(timeout=60.0) as client:
        import urllib.parse
        encoded_template = urllib.parse.quote(template, safe='')
        
        allocation_request = {
            "priority": 3,
            "requester_id": f"pytest-template-{test_config['test_session_id']}",
            "timeout_seconds": 30,
            "metadata": {
                "test": True,
                "template_test": template,
                "framework": "pytest"
            }
        }
        
        response = await client.post(
            f"{test_config['api_base']}/api/v1/pools/{encoded_template}/allocate",
            json=allocation_request
        )
        
        # Should either succeed or timeout gracefully
        assert response.status_code in [200, 201, 408, 504]


@pytest.mark.asyncio
async def test_error_handling(test_config):
    """Test error handling and validation."""
    async with httpx.AsyncClient(timeout=30.0) as client:
        # Test invalid template
        response = await client.post(
            f"{test_config['api_base']}/api/v1/pools/invalid:template/allocate",
            json={
                "priority": 3,
                "requester_id": f"pytest-error-{test_config['test_session_id']}",
                "timeout_seconds": 30
            }
        )
        assert response.status_code in [400, 404], "Invalid template should be rejected"
        
        # Test invalid priority
        response = await client.post(
            f"{test_config['api_base']}/api/v1/pools/ubuntu:20.04/allocate",
            json={
                "priority": 99,  # Invalid priority
                "requester_id": f"pytest-error-{test_config['test_session_id']}",
                "timeout_seconds": 30
            }
        )
        assert response.status_code == 422, "Invalid priority should be rejected"


@pytest.mark.asyncio
async def test_performance_metrics(test_config):
    """Test system performance metrics."""
    async with httpx.AsyncClient(timeout=30.0) as client:
        # Test API response time
        start_time = time.time()
        response = await client.get(f"{test_config['api_base']}/api/v1/pools/status")
        api_response_time = time.time() - start_time
        
        assert response.status_code == 200
        assert api_response_time < 5.0, f"API response too slow: {api_response_time:.3f}s"
        
        # Test concurrent requests
        async def make_status_request():
            async with httpx.AsyncClient(timeout=30.0) as client:
                return await client.get(f"{test_config['api_base']}/api/v1/pools/status")
        
        start_time = time.time()
        tasks = [make_status_request() for _ in range(5)]
        responses = await asyncio.gather(*tasks, return_exceptions=True)
        concurrent_time = time.time() - start_time
        
        successful_responses = sum(
            1 for r in responses 
            if hasattr(r, 'status_code') and r.status_code == 200
        )
        
        assert successful_responses >= 4, f"Only {successful_responses}/5 concurrent requests succeeded"
        assert concurrent_time < 10.0, f"Concurrent requests too slow: {concurrent_time:.3f}s"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
