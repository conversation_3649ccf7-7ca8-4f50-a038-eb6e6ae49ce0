"""
Real VM Management Routes Tests - No Mocks

This module tests VM management API routes using real Docker containers
instead of mocks, providing more reliable and realistic testing.
"""

import pytest
import json
import time
from fastapi.testclient import TestClient
from fastapi.websockets import WebSocketDisconnect

from api.v1.application import create_app
from api.models.vm_management import VMCreateRequest, VMType


class TestRealVMManagementRoutes:
    """Test VM Management REST API routes with real Docker containers"""

    @pytest.fixture
    def client(self, real_vm_service):
        """Create test client with real VM service"""
        app = create_app()
        
        # Override the VM service dependency with our real service
        from api.v1.routes.vm_management import get_vm_service
        app.dependency_overrides[get_vm_service] = lambda: real_vm_service
        
        return TestClient(app)

    @pytest.fixture
    def sample_vm_data(self):
        """Sample VM creation data"""
        return {
            "name": f"test-vm-{int(time.time())}",
            "template": "ubuntu:20.04",
            "vm_type": "docker",
            "memory_mb": 256,  # Smaller for testing
            "cpus": 1,
            "domain": "TurdParty",
            "description": "Test VM for real testing"
        }

    def test_get_vm_templates_real(self, client):
        """Test getting VM templates with real service"""
        response = client.get("/api/v1/vms/templates")
        
        assert response.status_code == 200
        templates = response.json()
        assert isinstance(templates, list)
        assert len(templates) > 0
        
        # Check template structure
        template = templates[0]
        assert "template_id" in template
        assert "name" in template
        assert "vm_type" in template
        assert "resource_requirements" in template

    def test_create_vm_real(self, client, sample_vm_data):
        """Test successful VM creation with real Docker container"""
        response = client.post("/api/v1/vms/", json=sample_vm_data)
        
        assert response.status_code == 201
        vm = response.json()
        assert vm["name"] == sample_vm_data["name"]
        assert vm["template"] == sample_vm_data["template"]
        assert vm["vm_type"] == sample_vm_data["vm_type"]
        assert "vm_id" in vm
        assert "created_at" in vm
        assert vm["status"] == "running"  # Should be running after creation
        
        # Cleanup: Delete the VM
        vm_id = vm["vm_id"]
        cleanup_response = client.delete(f"/api/v1/vms/{vm_id}?force=true")
        assert cleanup_response.status_code == 200

    def test_get_vm_by_id_real(self, client, sample_vm_data):
        """Test getting VM by ID with real container"""
        # Create a VM first
        create_response = client.post("/api/v1/vms/", json=sample_vm_data)
        vm_id = create_response.json()["vm_id"]
        
        try:
            # Get the VM
            response = client.get(f"/api/v1/vms/{vm_id}")
            
            assert response.status_code == 200
            vm = response.json()
            assert vm["vm_id"] == vm_id
            assert vm["name"] == sample_vm_data["name"]
            assert vm["status"] == "running"
            assert "ip_address" in vm  # Real container should have IP
            
        finally:
            # Cleanup
            client.delete(f"/api/v1/vms/{vm_id}?force=true")

    def test_vm_lifecycle_actions_real(self, client, sample_vm_data):
        """Test VM lifecycle actions with real containers"""
        # Create a VM first
        create_response = client.post("/api/v1/vms/", json=sample_vm_data)
        vm_id = create_response.json()["vm_id"]
        
        try:
            # Test stop action
            stop_response = client.post(f"/api/v1/vms/{vm_id}/action", json={"action": "stop"})
            assert stop_response.status_code == 200
            stop_result = stop_response.json()
            assert stop_result["action"] == "stop"
            assert stop_result["vm_id"] == vm_id
            
            # Verify VM is actually stopped
            vm_response = client.get(f"/api/v1/vms/{vm_id}")
            vm_data = vm_response.json()
            assert vm_data["status"] == "stopped"
            
            # Test suspend action
            suspend_response = client.post(f"/api/v1/vms/{vm_id}/action", json={"action": "suspend"})
            assert suspend_response.status_code == 200
            
            # Test resume action
            resume_response = client.post(f"/api/v1/vms/{vm_id}/action", json={"action": "resume"})
            assert resume_response.status_code == 200
            
            # Verify VM is running again
            vm_response = client.get(f"/api/v1/vms/{vm_id}")
            vm_data = vm_response.json()
            assert vm_data["status"] == "running"
            
        finally:
            # Cleanup
            client.delete(f"/api/v1/vms/{vm_id}?force=true")

    def test_list_vms_real(self, client, sample_vm_data):
        """Test listing VMs with real containers"""
        # Create a VM first
        create_response = client.post("/api/v1/vms/", json=sample_vm_data)
        vm_id = create_response.json()["vm_id"]
        
        try:
            # Test listing
            response = client.get("/api/v1/vms/")
            
            assert response.status_code == 200
            data = response.json()
            assert "vms" in data
            assert "total" in data
            assert data["total"] >= 1
            
            # Find our VM in the list
            vm_found = False
            for vm in data["vms"]:
                if vm["vm_id"] == vm_id:
                    vm_found = True
                    assert vm["name"] == sample_vm_data["name"]
                    break
            assert vm_found, "Created VM not found in list"
            
        finally:
            # Cleanup
            client.delete(f"/api/v1/vms/{vm_id}?force=true")

    def test_delete_vm_real(self, client, sample_vm_data):
        """Test VM deletion with real container cleanup"""
        # Create a VM first
        create_response = client.post("/api/v1/vms/", json=sample_vm_data)
        vm_id = create_response.json()["vm_id"]
        
        # Delete the VM
        response = client.delete(f"/api/v1/vms/{vm_id}?force=true")
        
        assert response.status_code == 200
        result = response.json()
        assert result["vm_id"] == vm_id
        
        # Verify VM is actually deleted
        get_response = client.get(f"/api/v1/vms/{vm_id}")
        assert get_response.status_code == 404

    def test_vm_resource_validation_real(self, client):
        """Test VM resource validation with real constraints"""
        # Test with very low memory (should fail)
        invalid_data = {
            "name": f"invalid-vm-{int(time.time())}",
            "template": "ubuntu:20.04",
            "vm_type": "docker",
            "memory_mb": 50,  # Too low
            "cpus": 1,
            "domain": "TurdParty"
        }
        
        response = client.post("/api/v1/vms/", json=invalid_data)
        assert response.status_code == 422  # Validation error
        
        # Test with invalid template
        invalid_template_data = {
            "name": f"invalid-template-{int(time.time())}",
            "template": "nonexistent:image",
            "vm_type": "docker",
            "memory_mb": 256,
            "cpus": 1,
            "domain": "TurdParty"
        }
        
        response = client.post("/api/v1/vms/", json=invalid_template_data)
        # This might succeed at API level but fail at Docker level
        # The important thing is it doesn't crash the service


class TestRealVMWebSocketRoutes:
    """Test VM WebSocket routes with real containers"""

    @pytest.fixture
    def client(self, real_vm_service, real_vm_metrics_service):
        """Create test client with real services"""
        app = create_app()
        
        # Override dependencies with real services
        from api.v1.routes.vm_management import get_vm_service, get_vm_metrics_service
        app.dependency_overrides[get_vm_service] = lambda: real_vm_service
        app.dependency_overrides[get_vm_metrics_service] = lambda: real_vm_metrics_service
        
        return TestClient(app)

    @pytest.fixture
    def sample_vm_data(self):
        """Sample VM creation data"""
        return {
            "name": f"ws-test-vm-{int(time.time())}",
            "template": "ubuntu:20.04",
            "vm_type": "docker",
            "memory_mb": 256,
            "cpus": 1,
            "domain": "TurdParty"
        }

    def test_vm_metrics_websocket_real(self, client, sample_vm_data):
        """Test WebSocket metrics streaming with real container"""
        # Create a VM first
        create_response = client.post("/api/v1/vms/", json=sample_vm_data)
        vm_id = create_response.json()["vm_id"]
        
        try:
            # Test WebSocket connection
            with client.websocket_connect(f"/api/v1/vms/{vm_id}/metrics/stream?vm_type=docker") as websocket:
                # Should receive real metrics data
                data = websocket.receive_json()
                assert "vm_id" in data
                assert "timestamp" in data
                assert "cpu_percent" in data
                assert "memory_percent" in data
                assert "status" in data
                assert data["vm_type"] == "docker"
                
                # Verify realistic values
                assert data["cpu_percent"] >= 0
                assert data["memory_percent"] >= 0
                assert data["memory_used_bytes"] > 0
                
        finally:
            # Cleanup
            client.delete(f"/api/v1/vms/{vm_id}?force=true")

    def test_command_execution_websocket_real(self, client, sample_vm_data):
        """Test WebSocket command execution with real container"""
        # Create a VM first
        create_response = client.post("/api/v1/vms/", json=sample_vm_data)
        vm_id = create_response.json()["vm_id"]
        
        try:
            # Test command execution WebSocket
            with client.websocket_connect(f"/api/v1/vms/{vm_id}/commands/execute") as websocket:
                # Send a real command
                command_data = {
                    "command": "echo 'Hello from real container'",
                    "working_directory": "/tmp"
                }
                websocket.send_json(command_data)
                
                # Should receive real command output
                response = websocket.receive_json()
                assert "type" in response
                assert response["type"] in ["command_output", "command_complete"]
                
                if response["type"] == "command_output":
                    assert "output" in response
                    assert "Hello from real container" in response["output"]
                
        finally:
            # Cleanup
            client.delete(f"/api/v1/vms/{vm_id}?force=true")

    def test_websocket_error_handling_real(self, client):
        """Test WebSocket error handling with non-existent VM"""
        # Test with non-existent VM
        try:
            with client.websocket_connect("/api/v1/vms/nonexistent/metrics/stream") as websocket:
                data = websocket.receive_json()
                # Should receive error message
                assert "error" in data or data.get("status") == "error"
        except WebSocketDisconnect:
            # Connection closed due to error, which is acceptable
            pass


class TestRealVMManagementPerformance:
    """Test VM management performance with real containers"""

    @pytest.fixture
    def client(self, real_vm_service):
        """Create test client with real VM service"""
        app = create_app()
        
        from api.v1.routes.vm_management import get_vm_service
        app.dependency_overrides[get_vm_service] = lambda: real_vm_service
        
        return TestClient(app)

    def test_concurrent_vm_creation_real(self, client):
        """Test concurrent VM creation with real containers"""
        import threading
        import time
        
        results = []
        vm_ids = []
        
        def create_vm(vm_index):
            vm_data = {
                "name": f"concurrent-vm-{vm_index}-{int(time.time())}",
                "template": "ubuntu:20.04",
                "vm_type": "docker",
                "memory_mb": 256,
                "cpus": 1,
                "domain": "TurdParty"
            }
            response = client.post("/api/v1/vms/", json=vm_data)
            results.append(response.status_code)
            if response.status_code == 201:
                vm_ids.append(response.json()["vm_id"])
        
        try:
            # Create multiple VMs concurrently (smaller number for real containers)
            threads = []
            for i in range(3):  # Reduced from 5 to avoid resource exhaustion
                thread = threading.Thread(target=create_vm, args=[i])
                threads.append(thread)
                thread.start()
            
            # Wait for all threads to complete
            for thread in threads:
                thread.join()
            
            # Most should succeed (allow for some failures due to resource constraints)
            success_count = sum(1 for status in results if status == 201)
            assert success_count >= 2, f"Expected at least 2 successes, got {success_count}"
            
        finally:
            # Cleanup all created VMs
            for vm_id in vm_ids:
                try:
                    client.delete(f"/api/v1/vms/{vm_id}?force=true")
                except Exception:
                    pass  # Ignore cleanup errors

    def test_vm_creation_performance_real(self, client):
        """Test VM creation performance with real containers"""
        import time
        
        vm_data = {
            "name": f"perf-test-vm-{int(time.time())}",
            "template": "ubuntu:20.04",
            "vm_type": "docker",
            "memory_mb": 256,
            "cpus": 1,
            "domain": "TurdParty"
        }
        
        # Measure creation time
        start_time = time.time()
        response = client.post("/api/v1/vms/", json=vm_data)
        creation_time = time.time() - start_time
        
        try:
            assert response.status_code == 201
            # VM creation should complete within reasonable time (30 seconds for real container)
            assert creation_time < 30, f"VM creation took {creation_time:.2f}s, expected <30s"
            
            vm_id = response.json()["vm_id"]
            
            # Verify VM is actually running
            get_response = client.get(f"/api/v1/vms/{vm_id}")
            assert get_response.status_code == 200
            assert get_response.json()["status"] == "running"
            
        finally:
            # Cleanup
            if response.status_code == 201:
                client.delete(f"/api/v1/vms/{response.json()['vm_id']}?force=true")
